{"total_endpoints": 190, "current_documented": 22, "prioritized_endpoints": {"1": ["GET /api/customer/bookings", "GET /api/customer/bookings/:bookingId/can-review", "GET /api/customer/bookings/:id", "GET /api/customer/bookings/history", "GET /api/customer/dashboard", "GET /api/customer/profile", "GET /api/public/fields", "GET /api/public/fields/:fieldId/promotions", "GET /api/public/fields/:fieldId/rating", "GET /api/public/fields/:fieldId/reviews", "GET /api/public/fields/:id", "GET /api/public/fields/:id/availability", "POST /api/auth/login", "POST /api/auth/register", "POST /api/customer/bookings", "PUT /api/customer/bookings/:id/cancel", "PUT /api/customer/profile"], "2": ["DELETE /api/customer/favorites/:fieldId", "DELETE /api/customer/notifications/:id", "GET /api/admin/bookings", "GET /api/admin/bookings/:id", "GET /api/admin/bookings/statistics", "GET /api/auth/hash-password/:password", "GET /api/auth/health", "GET /api/auth/profile", "GET /api/auth/reset-password/:token", "GET /api/auth/roles", "GET /api/auth/verification-status/:email", "GET /api/auth/verify", "GET /api/customer/favorites", "GET /api/customer/favorites/:fieldId/check", "GET /api/customer/favorites/availability", "GET /api/customer/favorites/count", "GET /api/customer/favorites/statistics", "GET /api/customer/notifications", "GET /api/customer/notifications/count", "GET /api/customer/notifications/statistics", "GET /api/customer/notifications/unread-count", "GET /api/staff/kasir/payments", "GET /api/staff/kasir/payments/:id", "GET /api/staff/kasir/payments/pending", "GET /api/staff/operator/bookings", "GET /api/staff/operator/bookings/:id", "GET /api/staff/operator/bookings/pending", "POST /api/auth/change-password", "POST /api/auth/forgot-password", "POST /api/auth/logout", "POST /api/auth/refresh", "POST /api/auth/reset-password", "POST /api/auth/send-verification", "POST /api/auth/validate-email", "POST /api/auth/validate-email-batch", "POST /api/auth/validate-email-quick", "POST /api/auth/verify-email", "POST /api/customer/favorites/:fieldId", "POST /api/staff/kasir/payments/debug", "POST /api/staff/kasir/payments/manual", "PUT /api/admin/bookings/:id/status", "PUT /api/customer/favorites/:fieldId/toggle", "PUT /api/customer/notifications/:id/read", "PUT /api/customer/notifications/read-all", "PUT /api/staff/kasir/payments/:id/confirm", "PUT /api/staff/operator/bookings/:id/complete", "PUT /api/staff/operator/bookings/:id/confirm"], "3": ["DELETE /api/admin/audit-logs/cleanup", "DELETE /api/admin/fields/:id", "DELETE /api/admin/notifications/:id", "DELETE /api/admin/promotions/:id", "DELETE /api/admin/settings/:key", "DELETE /api/admin/users/:id", "DELETE /api/customer/reviews/:id", "GET /api/", "GET /api/admin/analytics/business", "GET /api/admin/analytics/performance", "GET /api/admin/analytics/system", "GET /api/admin/audit-logs", "GET /api/admin/audit-logs/:id", "GET /api/admin/audit-logs/active-users", "GET /api/admin/audit-logs/export", "GET /api/admin/audit-logs/record/:tableName/:recordId", "GET /api/admin/audit-logs/statistics", "GET /api/admin/audit-logs/table/:tableName", "GET /api/admin/audit-logs/user/:userId", "GET /api/admin/auto-completion/config", "GET /api/admin/auto-completion/eligible", "GET /api/admin/auto-completion/stats", "GET /api/admin/fields", "GET /api/admin/fields/:id", "GET /api/admin/notifications", "GET /api/admin/notifications/:id/status", "GET /api/admin/notifications/statistics", "GET /api/admin/notifications/user/:userId", "GET /api/admin/promotions", "GET /api/admin/promotions/:id/usage", "GET /api/admin/promotions/analytics", "GET /api/admin/role-management/dashboard", "GET /api/admin/role-management/users", "GET /api/admin/settings", "GET /api/admin/settings/:key", "GET /api/admin/settings/category/:category", "GET /api/admin/settings/public", "GET /api/admin/users", "GET /api/admin/users/:id", "GET /api/auth/test-features", "GET /api/auth/test-smtp", "GET /api/customer/booking-history", "GET /api/customer/fields", "GET /api/customer/promotions", "GET /api/customer/promotions/:code", "GET /api/customer/recommendations", "GET /api/customer/reviews", "GET /api/customer/reviews/:id", "GET /api/customer/upcoming-bookings", "GET /api/public/app-config", "GET /api/public/database-status", "GET /api/public/field-locations", "GET /api/public/field-types", "GET /api/public/health", "GET /api/public/promotions", "GET /api/public/system-info", "GET /api/public/version", "GET /api/staff/kasir/bookings", "GET /api/staff/kasir/bookings/:id", "GET /api/staff/kasir/daily-report", "GET /api/staff/kasir/dashboard", "GET /api/staff/kasir/payment-methods", "GET /api/staff/kasir/pending-payments", "GET /api/staff/kasir/statistics", "GET /api/staff/operator/booking-actions", "GET /api/staff/operator/dashboard", "GET /api/staff/operator/field-statuses", "GET /api/staff/operator/fields", "GET /api/staff/operator/fields/:field_id/bookings", "GET /api/staff/operator/schedule/:date", "GET /api/staff/operator/schedule/today", "GET /api/staff/operator/statistics", "GET /api/staff/operator/today-schedule", "GET /api/staff/supervisor/analytics", "GET /api/staff/supervisor/audit-logs", "GET /api/staff/supervisor/dashboard", "GET /api/staff/supervisor/database-stats", "GET /api/staff/supervisor/error-logs", "GET /api/staff/supervisor/system-config", "GET /api/staff/supervisor/system-health", "GET /api/staff/supervisor/users", "POST /api/admin/auto-completion/manual/:id", "POST /api/admin/auto-completion/trigger", "POST /api/admin/fields", "POST /api/admin/notifications", "POST /api/admin/notifications/broadcast", "POST /api/admin/promotions", "POST /api/admin/role-management/request-change", "POST /api/admin/settings", "POST /api/admin/settings/initialize", "POST /api/customer/promotions/apply", "POST /api/customer/promotions/calculate", "POST /api/customer/promotions/validate", "POST /api/customer/reviews", "POST /api/staff/supervisor/staff", "POST /api/staff/supervisor/system-maintenance", "PUT /api/admin/fields/:id", "PUT /api/admin/promotions/:id", "PUT /api/admin/promotions/:id/toggle", "PUT /api/admin/role-management/change-role", "PUT /api/admin/settings/:key", "PUT /api/admin/settings/:key/reset", "PUT /api/admin/settings/bulk-update", "PUT /api/admin/users/:id", "PUT /api/customer/reviews/:id", "PUT /api/staff/operator/fields/:id/status", "PUT /api/staff/supervisor/users/:id/role"], "4": ["GET /api/enhanced/", "GET /api/enhanced/architecture", "GET /api/enhanced/features", "GET /api/enhanced/status", "GET /api/health", "GET /api/routes", "GET /api/test/health", "GET /api/test/routes"], "5": ["GET /api/public/debug/table/:tableName", "GET /api/public/debug/test-promotion", "GET /api/public/debug/test-settings", "GET /api/test/admin", "GET /api/test/auth", "GET /api/test/customer", "GET /api/test/database", "GET /api/test/environment", "GET /api/test/memory", "GET /api/test/public", "GET /api/test/staff"]}, "recommendations": {"critical": 17, "high": 47, "medium": 107, "total_recommended": 171, "coverage_target": "90.0%"}, "generated_at": "2025-06-06T10:07:33.791Z"}