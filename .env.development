# Development Environment Configuration for Booking Futsal Frontend

# API Configuration - Using production backend (LOCAL BACKEND HAS DB ISSUES)
# VITE_API_URL=http://localhost:3000/api  # Local backend has PostgreSQL connection issues
VITE_API_URL=https://booking-futsal-production.up.railway.app/api  # Production backend working ✅

# Application Configuration
VITE_APP_NAME=Booking Futsal System (Development)
VITE_APP_VERSION=1.0.0-dev

# Development Configuration
VITE_NODE_ENV=development
VITE_DEBUG_MODE=true
VITE_ENABLE_LOGGING=true

# Authentication Configuration
VITE_AUTH_TOKEN_KEY=auth_token
VITE_AUTH_STORAGE_TYPE=localStorage

# Development Server Configuration
VITE_DEV_SERVER_PORT=5173
VITE_DEV_SERVER_HOST=localhost

# CORS Configuration for Development
VITE_CORS_ENABLED=true
VITE_CREDENTIALS_INCLUDE=true
