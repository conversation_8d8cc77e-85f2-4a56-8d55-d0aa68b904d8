{"name": "booking-futsal-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "dev:prod": "vite --mode production", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "start": "vite preview --port $PORT", "setup": "node scripts/dev-setup.js", "test:dev": "echo 'Open browser console and run: testDevelopmentEnvironment()'", "clean": "rm -rf dist node_modules/.vite", "health": "curl -f http://localhost:5173 || echo 'Frontend not running'"}, "dependencies": {"@tailwindcss/vite": "^4.1.7", "axios": "^1.9.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.27.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "terser": "^5.40.0", "vite": "^6.3.5"}}