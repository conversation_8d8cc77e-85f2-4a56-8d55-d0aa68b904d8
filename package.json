{"name": "booking_futsal", "version": "1.0.0", "main": "server.js", "engines": {"node": ">=18.20.4 <19.0.0", "npm": ">=9.0.0"}, "scripts": {"start": "cross-env NODE_ENV=production node server.js", "dev": "npx dotenv-cli -e .env.development nodemon server.js", "prod": "npx dotenv-cli -e .env.production node server.js", "build": "echo 'Build completed - Node.js application ready for production'", "postinstall": "npm cache clean --force", "health": "curl -f http://localhost:5000/api/test/health || exit 1"}, "dependencies": {"bcryptjs": "^3.0.2", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "morgan": "^1.10.0", "node-cron": "^4.0.5", "node-fetch": "^3.3.2", "nodemailer": "^7.0.3", "pg": "^8.15.6", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "validator": "^13.12.0"}, "devDependencies": {"cross-env": "^7.0.3", "dotenv-cli": "^8.0.0", "nodemon": "^3.1.10"}}