# Production Environment Configuration for Booking Futsal Frontend

# API Configuration - Using Railway production backend
VITE_API_URL=https://booking-futsal-production.up.railway.app/api

# Application Configuration
VITE_APP_NAME=Booking Futsal System
VITE_APP_VERSION=1.0.0

# Production Configuration
VITE_NODE_ENV=production
VITE_DEBUG_MODE=false
VITE_ENABLE_LOGGING=false

# Authentication Configuration
VITE_AUTH_TOKEN_KEY=auth_token
VITE_AUTH_STORAGE_TYPE=localStorage

# CORS Configuration for Production
VITE_CORS_ENABLED=true
VITE_CREDENTIALS_INCLUDE=true
