{"openapi": "3.0.0", "info": {"title": "Enhanced Futsal Booking System API", "version": "2.0.0", "description": "API untuk sistem booking lapangan futsal dengan fitur lengkap"}, "servers": [{"url": "https://booking-futsal-production.up.railway.app", "description": "Production Server (Railway)"}, {"url": "http://localhost:5000", "description": "Development Server"}], "paths": {"/api/auth/register": {"post": {"tags": ["Authentication"], "summary": "Register pengguna baru ⚪ PUBLIC", "description": "Endpoint untuk mendaftarkan pengguna baru dengan role default penyewa", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name", "email", "password", "phone"], "properties": {"name": {"type": "string", "example": "<PERSON>", "description": "<PERSON><PERSON> le<PERSON>"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>", "description": "<PERSON><PERSON> un<PERSON>"}, "password": {"type": "string", "example": "password123", "description": "Password minimal 6 karakter"}, "phone": {"type": "string", "example": "081234567890", "description": "Nomor telepon"}, "role": {"type": "string", "enum": ["user", "penyewa"], "default": "user", "description": "Role (opsional)"}}}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> be<PERSON>", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Registration successful"}, "user": {"$ref": "#/components/schemas/User"}, "token": {"type": "string", "description": "JWT token (development only)"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "409": {"$ref": "#/components/responses/Conflict"}}}}, "/api/auth/login": {"post": {"tags": ["Authentication"], "summary": "Login pengguna ⚪ PUBLIC", "description": "Endpoint untuk autentikasi pengguna dengan email dan password", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>", "description": "<PERSON><PERSON>"}, "password": {"type": "string", "example": "password123", "description": "Password pengguna"}}}}}}, "responses": {"200": {"description": "<PERSON><PERSON> ber<PERSON>il", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON> ber<PERSON>il"}, "data": {"type": "object", "properties": {"token": {"type": "string", "description": "JWT token"}, "user": {"$ref": "#/components/schemas/User"}}}}}}}}, "400": {"description": "Password salah atau email tidak ditemukan", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "string", "example": "Password salah"}}}}}}}}}, "/api/auth/logout": {"post": {"tags": ["Authentication"], "summary": "Logout pengguna 🔵 AUTHENTICATED", "description": "Endpoint untuk logout pengguna dan menghapus session/token", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "responses": {"200": {"description": "Lo<PERSON>ut berhasil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Logout successful"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/api/auth/profile": {"get": {"tags": ["Authentication"], "summary": "Get profil pengguna 🔵 AUTHENTICATED", "description": "Endpoint untuk mendapatkan profil pengguna yang sedang login", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON> be<PERSON><PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/components/schemas/User"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/api/auth/refresh": {"post": {"tags": ["Authentication"], "summary": "Refresh JWT token 🔵 AUTHENTICATED", "description": "Endpoint untuk memperbarui JWT token yang akan expired", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "responses": {"200": {"description": "Token berhasil di-refresh", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON> refreshed successfully"}, "data": {"type": "object", "properties": {"token": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "description": "JWT token baru yang sudah di-refresh"}, "expires_in": {"type": "string", "example": "7d", "description": "<PERSON><PERSON><PERSON> expired token (7 hari)"}, "user": {"type": "object", "properties": {"id": {"type": "integer", "example": 123}, "email": {"type": "string", "example": "<EMAIL>"}, "name": {"type": "string", "example": "<PERSON>"}, "role": {"type": "string", "example": "penyewa"}}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/api/auth/roles": {"get": {"tags": ["Authentication"], "summary": "Mendapatkan daftar role sistem ⚪ PUBLIC", "description": "Endpoint untuk mendapatkan semua role yang tersedia dalam sistem enhanced 6-level hierarchy", "responses": {"200": {"description": "<PERSON><PERSON><PERSON> role be<PERSON><PERSON><PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"roles": {"type": "array", "items": {"type": "object", "properties": {"value": {"type": "string", "example": "penyewa"}, "label": {"type": "string", "example": "Customer (Penyewa)"}, "level": {"type": "integer", "example": 2}, "description": {"type": "string", "example": "Customer yang dapat melakukan booking"}}}}, "hierarchy": {"type": "array", "items": {"type": "string"}, "example": ["pengunjung", "penyewa", "staff_kasir", "operator_lapangan", "manajer_futsal", "supervisor_sistem"]}}}}}}}}}}}, "/api/auth/change-password": {"post": {"tags": ["Authentication"], "summary": "Ubah password pengguna 🔵 AUTHENTICATED", "description": "Endpoint untuk mengubah password pengguna yang sedang login", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["current_password", "new_password", "confirm_password"], "properties": {"current_password": {"type": "string", "example": "oldpassword123", "description": "Password saat ini"}, "new_password": {"type": "string", "example": "newpassword123", "description": "Password baru"}, "confirm_password": {"type": "string", "example": "newpassword123", "description": "Konfirmasi password baru"}}}}}}, "responses": {"200": {"description": "Password ber<PERSON>il diubah", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Password changed successfully"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/api/auth/verify": {"get": {"tags": ["Authentication"], "summary": "Verify authentication status 🔵 AUTHENTICATED", "description": "Endpoint untuk memverifikasi status autentikasi pengguna", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "responses": {"200": {"description": "Authentication status berhasil diverifikasi", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "User authenticated"}, "user": {"$ref": "#/components/schemas/User"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/api/auth/forgot-password": {"post": {"tags": ["Authentication"], "summary": "Request reset password ⚪ PUBLIC", "description": "Endpoint untuk meminta reset password melalui email", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>", "description": "Email pengguna yang akan direset passwordnya"}}}}}}, "responses": {"200": {"description": "Email reset password ber<PERSON><PERSON> di<PERSON>rim", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Password reset email sent successfully"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/auth/send-verification": {"post": {"tags": ["Authentication"], "summary": "Send email verification ⚪ PUBLIC", "description": "Endpoint untuk mengirim email verifikasi ke pengguna", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}}}}}}, "responses": {"200": {"description": "<PERSON><PERSON> veri<PERSON>i be<PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Verification email sent successfully"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/public/system-info": {"get": {"tags": ["Public"], "summary": "Get informasi sistem ⚪ PUBLIC", "description": "Endpoint untuk mendapatkan informasi sistem publik", "responses": {"200": {"description": "Informasi sistem berhasil diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"app_name": {"type": "string", "example": "Enhanced Futsal Booking System"}, "version": {"type": "string", "example": "2.0.0"}, "environment": {"type": "string", "example": "production"}, "server_time": {"type": "string", "format": "date-time"}, "timezone": {"type": "string", "example": "Asia/Jakarta"}, "features": {"type": "object", "properties": {"role_based_access": {"type": "boolean", "example": true}, "auto_completion": {"type": "boolean", "example": true}, "notifications": {"type": "boolean", "example": true}, "reviews": {"type": "boolean", "example": true}, "promotions": {"type": "boolean", "example": true}}}}}}}}}}}}}, "/api/public/database-status": {"get": {"tags": ["Public"], "summary": "Get status database ⚪ PUBLIC", "description": "Endpoint untuk mengecek status database dan tabel", "responses": {"200": {"description": "Status database berhasil diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"database_connected": {"type": "boolean", "example": true}, "total_tables": {"type": "integer", "example": 15}, "missing_tables": {"type": "array", "items": {"type": "string"}}, "existing_tables": {"type": "array", "items": {"type": "string"}}, "migration_status": {"type": "string", "example": "completed"}, "last_check": {"type": "string", "format": "date-time"}}}}}}}}, "500": {"description": "Database connection error", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Database connection failed"}}}}}}}}}, "/api/public/fields": {"get": {"summary": "Mendapatkan daftar lapangan yang tersedia ⚪ PUBLIC", "description": "Endpoint publik untuk mendapatkan semua lapangan futsal dengan informasi lengkap", "tags": ["Public"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "<PERSON><PERSON> halaman"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10}, "description": "Jumlah item per halaman"}, {"in": "query", "name": "search", "schema": {"type": "string"}, "description": "<PERSON><PERSON> kunci pen<PERSON>ian"}, {"in": "query", "name": "type", "schema": {"type": "string", "enum": ["futsal", "mini_soccer", "basketball"]}, "description": "<PERSON><PERSON> be<PERSON><PERSON> jenis <PERSON>n"}, {"in": "query", "name": "location", "schema": {"type": "string"}, "description": "Filter berda<PERSON> lokasi"}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> lapangan ber<PERSON><PERSON> di<PERSON>bil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> lapangan ber<PERSON><PERSON> di<PERSON>bil"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Field"}}, "pagination": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/public/fields/{id}": {"get": {"tags": ["Public"], "summary": "Get detail lapangan ⚪ PUBLIC", "description": "Endpoint untuk mendapatkan detail lengkap lapangan berdasarkan ID", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID lapangan"}], "responses": {"200": {"description": "Detail lapangan ber<PERSON>il diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/components/schemas/Field"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/public/fields/{id}/availability": {"get": {"tags": ["Public"], "summary": "Cek ketersediaan lapangan ⚪ PUBLIC", "description": "Endpoint untuk mengecek ketersediaan lapangan pada tanggal tertentu", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID lapangan"}, {"in": "query", "name": "date", "required": true, "schema": {"type": "string", "format": "date", "example": "2024-12-01"}, "description": "<PERSON><PERSON> yang ingin dicek"}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> lapangan ber<PERSON>il di<PERSON>bil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"field_id": {"type": "integer", "example": 1}, "date": {"type": "string", "format": "date", "example": "2024-12-01"}, "available_slots": {"type": "array", "items": {"type": "object", "properties": {"start_time": {"type": "string", "example": "10:00"}, "end_time": {"type": "string", "example": "11:00"}, "is_available": {"type": "boolean"}, "price": {"type": "string", "example": "150000.00"}}}}}}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/public/field-types": {"get": {"tags": ["Public"], "summary": "Get tipe lapangan ⚪ PUBLIC", "description": "Endpoint untuk mendapatkan daftar tipe lapangan yang tersedia", "responses": {"200": {"description": "Daftar tipe lapangan berhasil diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Futsal Indoor"}, "description": {"type": "string", "example": "Lapangan futsal indoor dengan rumput sintetis"}, "price_per_hour": {"type": "string", "example": "100000.00"}}}}}}}}}}}}, "/api/public/field-locations": {"get": {"tags": ["Public"], "summary": "Get lokasi lapangan ⚪ PUBLIC", "description": "Endpoint untuk mendapatkan daftar lokasi lapangan yang tersedia", "responses": {"200": {"description": "Daftar lokasi lapangan ber<PERSON>il diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"location": {"type": "string", "example": "Jakarta Selatan"}, "count": {"type": "integer", "example": 5}}}}}}}}}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/api/public/health": {"get": {"tags": ["Public"], "summary": "Health check ⚪ PUBLIC", "description": "Endpoint untuk health check sistem", "responses": {"200": {"description": "<PERSON><PERSON><PERSON> ber<PERSON>lan dengan baik", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "API is running"}, "timestamp": {"type": "string", "format": "date-time"}, "uptime": {"type": "number", "example": 3600.5}}}}}}}}}, "/api/public/version": {"get": {"tags": ["Public"], "summary": "Get versi aplikasi ⚪ PUBLIC", "description": "Endpoint untuk mendapatkan versi aplikasi dan informasi build", "responses": {"200": {"description": "Informasi versi ber<PERSON>il diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"version": {"type": "string", "example": "2.0.0"}, "build": {"type": "string", "example": "20241206"}, "environment": {"type": "string", "example": "production"}, "node_version": {"type": "string", "example": "18.17.0"}}}}}}}}}}}, "/api/public/fields/{fieldId}/reviews": {"get": {"tags": ["Public"], "summary": "Get review lapangan ⚪ PUBLIC", "description": "Endpoint untuk mendapatkan daftar review lapangan dari customer", "parameters": [{"in": "path", "name": "fieldId", "required": true, "schema": {"type": "integer"}, "description": "ID lapangan"}, {"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "<PERSON><PERSON> halaman"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10}, "description": "Jumlah item per halaman"}], "responses": {"200": {"description": "Daftar review berhasil diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "user_name": {"type": "string"}, "rating": {"type": "integer", "minimum": 1, "maximum": 5}, "comment": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}}}}, "pagination": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/public/fields/{fieldId}/rating": {"get": {"tags": ["Public"], "summary": "Get rating lapangan ⚪ PUBLIC", "description": "Endpoint untuk mendapatkan ringkasan rating lapangan", "parameters": [{"in": "path", "name": "fieldId", "required": true, "schema": {"type": "integer"}, "description": "ID lapangan"}], "responses": {"200": {"description": "Rating lapangan ber<PERSON><PERSON> diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"field_id": {"type": "integer", "example": 1}, "average_rating": {"type": "string", "example": "4.5"}, "total_reviews": {"type": "integer", "example": 25}, "rating_distribution": {"type": "object", "properties": {"1": {"type": "integer", "example": 0}, "2": {"type": "integer", "example": 0}, "3": {"type": "integer", "example": 2}, "4": {"type": "integer", "example": 8}, "5": {"type": "integer", "example": 15}}}}}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/public/promotions": {"get": {"tags": ["Public"], "summary": "Get promosi yang tersedia ⚪ PUBLIC", "description": "Endpoint untuk mendapatkan daftar promosi yang sedang aktif", "responses": {"200": {"description": "Daftar promosi ber<PERSON><PERSON> di<PERSON>bil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Diskon Weekend"}, "description": {"type": "string", "example": "Diskon 20% untuk booking weekend"}, "code": {"type": "string", "example": "WEEKEND20"}, "type": {"type": "string", "enum": ["percentage", "fixed"], "example": "percentage"}, "value": {"type": "string", "example": "20.00"}, "valid_from": {"type": "string", "format": "date-time"}, "valid_until": {"type": "string", "format": "date-time"}}}}}}}}}}}}, "/api/public/fields/{fieldId}/promotions": {"get": {"tags": ["Public"], "summary": "Get promosi untuk lapangan tertentu ⚪ PUBLIC", "description": "Endpoint untuk mendapatkan promosi yang berlaku untuk lapangan tertentu", "parameters": [{"in": "path", "name": "fieldId", "required": true, "schema": {"type": "integer"}, "description": "ID lapangan"}, {"in": "query", "name": "date", "schema": {"type": "string", "format": "date", "example": "2024-12-01"}, "description": "Tanggal booking"}, {"in": "query", "name": "start_time", "schema": {"type": "string", "example": "10:00"}, "description": "<PERSON><PERSON><PERSON> mulai booking"}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> lapangan ber<PERSON><PERSON> di<PERSON>bil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "code": {"type": "string"}, "type": {"type": "string"}, "value": {"type": "string"}, "applicable_fields": {"type": "array", "items": {"type": "integer"}}}}}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/customer/profile": {"get": {"tags": ["Customer"], "summary": "Get profil customer 🔵 CUSTOMER", "description": "Endpoint untuk mendapatkan profil customer yang sedang login", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "responses": {"200": {"description": "Profil customer berhasil diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/components/schemas/User"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}, "put": {"tags": ["Customer"], "summary": "Update profil customer 🔵 CUSTOMER", "description": "Endpoint untuk mengupdate profil customer yang sedang login", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "example": "<PERSON>", "description": "Nama lengkap customer"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>", "description": "Email customer"}, "phone": {"type": "string", "example": "081234567890", "description": "Nomor telepon customer"}}}}}}, "responses": {"200": {"description": "Profil ber<PERSON>il diupdate", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Profile updated successfully"}, "data": {"$ref": "#/components/schemas/User"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/api/customer/bookings": {"post": {"tags": ["Customer"], "summary": "Buat booking baru 🔵 CUSTOMER", "description": "Endpoint untuk membuat booking lapangan baru", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["field_id", "date", "start_time", "end_time", "name", "phone"], "properties": {"field_id": {"type": "integer", "example": 1, "description": "ID lapangan"}, "date": {"type": "string", "format": "date", "example": "2024-12-01", "description": "Tanggal booking"}, "start_time": {"type": "string", "example": "10:00", "description": "<PERSON><PERSON><PERSON> mulai (HH:MM)"}, "end_time": {"type": "string", "example": "12:00", "description": "<PERSON><PERSON><PERSON> (HH:MM)"}, "name": {"type": "string", "example": "<PERSON>", "description": "<PERSON><PERSON>"}, "phone": {"type": "string", "example": "081234567890", "description": "Nomor telepon"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>", "description": "Email (opsional)"}, "notes": {"type": "string", "example": "Booking untuk turnamen", "description": "Catatan tambahan"}}}}}}, "responses": {"201": {"description": "Booking berhasil dibuat", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Booking created successfully"}, "data": {"$ref": "#/components/schemas/Booking"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "409": {"description": "Konflik waktu booking", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "string", "example": "Time slot already booked"}}}}}}}}, "get": {"tags": ["Customer"], "summary": "Get daftar booking customer 🔵 CUSTOMER", "description": "Endpoint untuk mendapatkan semua booking milik customer yang sedang login", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "<PERSON><PERSON> halaman"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10}, "description": "Jumlah item per halaman"}, {"in": "query", "name": "status", "schema": {"type": "string", "enum": ["pending", "confirmed", "completed", "cancelled"]}, "description": "Filter berdasarkan status booking"}], "responses": {"200": {"description": "Daftar booking ber<PERSON><PERSON> diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Booking"}}, "pagination": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/api/customer/bookings/history": {"get": {"tags": ["Customer"], "summary": "Get riwayat booking customer 🔵 CUSTOMER", "description": "Endpoint untuk mendapatkan riwayat booking customer dengan pagination dan filter", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "<PERSON><PERSON> halaman"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10}, "description": "Jumlah item per halaman"}, {"in": "query", "name": "status", "schema": {"type": "string", "enum": ["pending", "confirmed", "completed", "cancelled"]}, "description": "Filter berdasarkan status booking"}, {"in": "query", "name": "date_from", "schema": {"type": "string", "format": "date"}, "description": "Filter tanggal mulai"}, {"in": "query", "name": "date_to", "schema": {"type": "string", "format": "date"}, "description": "Filter tanggal akhir"}], "responses": {"200": {"description": "Riwayat booking berhasil diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Booking"}}, "pagination": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/api/customer/bookings/{id}": {"get": {"tags": ["Customer"], "summary": "Get detail booking customer 🔵 CUSTOMER", "description": "Endpoint untuk mendapatkan detail booking berdasarkan ID", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID booking"}], "responses": {"200": {"description": "Detail booking ber<PERSON><PERSON> diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/components/schemas/Booking"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/customer/bookings/{id}/cancel": {"put": {"tags": ["Customer"], "summary": "Batalkan booking customer 🔵 CUSTOMER", "description": "Endpoint untuk membatalkan booking yang sudah dibuat", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID booking yang akan di<PERSON>an"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["reason"], "properties": {"reason": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON> hadir", "description": "Alasan pembatalan"}}}}}}, "responses": {"200": {"description": "Booking be<PERSON><PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Booking cancelled successfully"}, "data": {"type": "object", "properties": {"booking_id": {"type": "integer"}, "status": {"type": "string", "example": "cancelled"}, "cancelled_at": {"type": "string", "format": "date-time"}, "cancellation_reason": {"type": "string"}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/customer/booking-history": {"get": {"tags": ["Customer"], "summary": "Get riwayat booking lengkap 🔵 CUSTOMER", "description": "Endpoint untuk mendapatkan riwayat booking lengkap customer dengan pagination", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "Halaman data"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10}, "description": "Jumlah data per halaman"}, {"in": "query", "name": "status", "schema": {"type": "string", "enum": ["pending", "confirmed", "completed", "cancelled"]}, "description": "Filter berdasarkan status"}, {"in": "query", "name": "date_from", "schema": {"type": "string", "format": "date"}, "description": "Filter tanggal mulai"}, {"in": "query", "name": "date_to", "schema": {"type": "string", "format": "date"}, "description": "Filter tanggal akhir"}], "responses": {"200": {"description": "Riwayat booking berhasil diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Booking"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/api/customer/upcoming-bookings": {"get": {"tags": ["Customer"], "summary": "Get booking mendatang 🔵 CUSTOMER", "description": "Endpoint untuk mendapatkan booking yang akan datang", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "responses": {"200": {"description": "Booking mendatang ber<PERSON>il diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Booking"}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/api/customer/dashboard": {"get": {"tags": ["Customer"], "summary": "Get dashboard customer 🔵 CUSTOMER", "description": "Endpoint untuk mendapatkan data dashboard customer dengan statistik booking", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "responses": {"200": {"description": "Dashboard customer ber<PERSON><PERSON> di<PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"customer_info": {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string"}, "total_bookings": {"type": "integer"}}}, "booking_stats": {"type": "object", "properties": {"pending": {"type": "integer"}, "confirmed": {"type": "integer"}, "completed": {"type": "integer"}}}, "recent_bookings": {"type": "array", "items": {"$ref": "#/components/schemas/Booking"}}, "favorite_fields": {"type": "array", "items": {"$ref": "#/components/schemas/Field"}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/api/customer/notifications": {"get": {"tags": ["Customer"], "summary": "Get notifikasi customer 🔵 CUSTOMER", "description": "Endpoint untuk mendapatkan daftar notifikasi customer", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10}}, {"in": "query", "name": "unread_only", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> notif<PERSON> ber<PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "title": {"type": "string"}, "message": {"type": "string"}, "type": {"type": "string"}, "is_read": {"type": "boolean"}, "created_at": {"type": "string", "format": "date-time"}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/api/customer/favorites": {"get": {"tags": ["Customer"], "summary": "Get lapangan favorit 🔵 CUSTOMER", "description": "Endpoint untuk mendapatkan daftar lapangan favorit customer", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "responses": {"200": {"description": "Daftar favorit ber<PERSON><PERSON> diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Field"}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/api/customer/notifications/count": {"get": {"tags": ["Customer"], "summary": "Get jumlah notifikasi belum dibaca 🔵 CUSTOMER", "description": "Endpoint untuk mendapatkan jumlah notifikasi yang belum dibaca", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"unread_count": {"type": "integer", "example": 5}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/api/customer/notifications/{id}/read": {"put": {"tags": ["Customer"], "summary": "Tandai notifikasi sebagai dibaca 🔵 CUSTOMER", "description": "Endpoint untuk menandai notifikasi tertentu sebagai sudah dibaca", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID notifikasi"}], "responses": {"200": {"description": "Notifikasi berhasil ditandai sebagai dibaca", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Notification marked as read"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/customer/reviews": {"get": {"tags": ["Customer"], "summary": "Get daftar review customer 🔵 CUSTOMER", "description": "Endpoint untuk mendapatkan semua review yang dibuat oleh customer", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10}}], "responses": {"200": {"description": "Daftar review berhasil diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "field_name": {"type": "string"}, "rating": {"type": "integer"}, "comment": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}, "post": {"tags": ["Customer"], "summary": "Buat review baru 🔵 CUSTOMER", "description": "Endpoint untuk membuat review lapangan setelah booking selesai", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["field_id", "booking_id", "rating", "comment"], "properties": {"booking_id": {"type": "integer", "example": 123}, "field_id": {"type": "integer", "example": 1}, "rating": {"type": "integer", "minimum": 1, "maximum": 5, "example": 5}, "comment": {"type": "string", "example": "Lapangan sangat bagus dan bersih"}}}}}}, "responses": {"201": {"description": "Review berhasil dibuat", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Review created successfully"}, "data": {"type": "object", "properties": {"id": {"type": "integer"}, "rating": {"type": "integer"}, "comment": {"type": "string"}}, "$ref": "#/components/schemas/Review"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/customer/reviews/{id}": {"get": {"tags": ["Customer"], "summary": "Get detail review 🔵 CUSTOMER", "description": "Endpoint untuk mendapatkan detail review berdasarkan ID", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID review"}], "responses": {"200": {"description": "Detail review berhasil diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"id": {"type": "integer"}, "field_name": {"type": "string"}, "rating": {"type": "integer"}, "comment": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}}}, "put": {"tags": ["Customer"], "summary": "Update review 🔵 CUSTOMER", "description": "Endpoint untuk mengupdate review yang sudah dibuat", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID review yang akan diupdate"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"rating": {"type": "integer", "minimum": 1, "maximum": 5, "example": 4}, "comment": {"type": "string", "example": "Update: <PERSON><PERSON><PERSON> bagus tapi agak ramai"}}}}}}, "responses": {"200": {"description": "Review berhasil diupdate", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Review updated successfully"}, "data": {"$ref": "#/components/schemas/Review"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}}}, "delete": {"tags": ["Customer"], "summary": "Hapus review 🔵 CUSTOMER", "description": "Endpoint untuk menghapus review yang sudah dibuat", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID review yang akan di<PERSON>pus"}], "responses": {"200": {"description": "Review berhasil dihapus", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Review deleted successfully"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/customer/bookings/{bookingId}/can-review": {"get": {"tags": ["Customer"], "summary": "Cek apakah customer bisa review booking 🔵 CUSTOMER", "description": "Endpoint untuk mengecek apakah customer dapat memberikan review untuk booking tertentu", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "path", "name": "bookingId", "required": true, "schema": {"type": "integer"}, "description": "ID booking yang akan dicek"}], "responses": {"200": {"description": "Status review berhasil dicek", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"can_review": {"type": "boolean", "example": true}, "booking_id": {"type": "integer", "example": 123}, "booking_status": {"type": "string", "example": "completed"}, "already_reviewed": {"type": "boolean", "example": false}, "reason": {"type": "string", "example": "Booking completed and not yet reviewed"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/customer/favorites/{fieldId}": {"post": {"tags": ["Customer"], "summary": "Tambah lapangan ke favorit 🔵 CUSTOMER", "description": "Endpoint untuk menambahkan lapangan ke daftar favorit customer", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "path", "name": "fieldId", "required": true, "schema": {"type": "integer"}, "description": "ID lapangan yang akan ditambahkan ke favorit"}], "responses": {"200": {"description": "Lapangan berhasil ditambahkan ke favorit", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Field added to favorites"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}}}, "delete": {"tags": ["Customer"], "summary": "Hapus lapangan dari favorit 🔵 CUSTOMER", "description": "Endpoint untuk menghapus lapangan dari daftar favorit customer", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "path", "name": "fieldId", "required": true, "schema": {"type": "integer"}, "description": "ID lapangan yang akan dihapus dari favorit"}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> ber<PERSON><PERSON> di<PERSON>pus dari favorit", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON> removed from favorites successfully"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/customer/favorites/{fieldId}/toggle": {"put": {"tags": ["Customer"], "summary": "Toggle status favorit lapangan 🔵 CUSTOMER", "description": "Endpoint untuk toggle status favorit lapangan (add/remove)", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "path", "name": "fieldId", "required": true, "schema": {"type": "integer"}, "description": "ID lapangan"}], "responses": {"200": {"description": "Status favorit berhasil di-toggle", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Field favorite status toggled"}, "data": {"type": "object", "properties": {"is_favorite": {"type": "boolean", "example": true}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/api/customer/favorites/{fieldId}/check": {"get": {"tags": ["Customer"], "summary": "Cek status favorit lapangan 🔵 CUSTOMER", "description": "Endpoint untuk mengecek apakah lapangan sudah difavoritkan", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "path", "name": "fieldId", "required": true, "schema": {"type": "integer"}, "description": "ID lapangan"}], "responses": {"200": {"description": "Status favorit ber<PERSON><PERSON> dicek", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"is_favorite": {"type": "boolean", "example": true}, "field_id": {"type": "integer", "example": 1}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/api/customer/favorites/availability": {"get": {"tags": ["Customer"], "summary": "Get favorit dengan info ketersediaan 🔵 CUSTOMER", "description": "Endpoint untuk mendapatkan lapangan favorit dengan informasi ketersediaan", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "date", "schema": {"type": "string", "format": "date", "example": "2024-12-01"}, "description": "Tanggal untuk cek ketersediaan"}], "responses": {"200": {"description": "Favorit dengan ketersediaan berhasil diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"field_id": {"type": "integer"}, "field_name": {"type": "string"}, "availability": {"type": "array", "items": {"type": "object"}}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/api/customer/favorites/statistics": {"get": {"tags": ["Customer"], "summary": "Get statistik favorit 🔵 CUSTOMER", "description": "Endpoint untuk mendapatkan statistik lapangan favorit customer", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "responses": {"200": {"description": "Statistik favorit ber<PERSON><PERSON> di<PERSON>bil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"total_favorites": {"type": "integer", "example": 5}, "most_booked_favorite": {"type": "object"}, "average_rating": {"type": "number", "example": 4.5}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/api/customer/favorites/count": {"get": {"tags": ["Customer"], "summary": "Get jumlah favorit 🔵 CUSTOMER", "description": "Endpoint untuk mendapatkan jumlah lapangan favorit customer", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> favorit ber<PERSON><PERSON> di<PERSON>bil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"count": {"type": "integer", "example": 5}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/api/customer/recommendations": {"get": {"tags": ["Customer"], "summary": "Get rekomendasi lapangan 🔵 CUSTOMER", "description": "Endpoint untuk mendapatkan rekomendasi lapangan berdasarkan preferensi customer", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "limit", "schema": {"type": "integer", "default": 5}, "description": "<PERSON><PERSON><PERSON> rekomendasi yang di<PERSON>an"}], "responses": {"200": {"description": "Reko<PERSON><PERSON><PERSON> lapangan ber<PERSON><PERSON> di<PERSON>bil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"field_id": {"type": "integer"}, "field_name": {"type": "string"}, "rating": {"type": "number"}, "recommendation_score": {"type": "number"}, "reason": {"type": "string"}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/api/customer/promotions": {"get": {"tags": ["Customer"], "summary": "Get promosi yang tersedia untuk customer 🔵 CUSTOMER", "description": "Endpoint untuk mendapatkan daftar promosi yang dapat digunakan customer", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "responses": {"200": {"description": "Daftar promosi ber<PERSON><PERSON> di<PERSON>bil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "code": {"type": "string"}, "type": {"type": "string"}, "value": {"type": "string"}, "valid_until": {"type": "string", "format": "date-time"}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/api/customer/promotions/{code}": {"get": {"tags": ["Customer"], "summary": "Get detail promosi berdasarkan kode 🔵 CUSTOMER", "description": "Endpoint untuk mendapatkan detail promosi berdasarkan kode promosi", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "path", "name": "code", "required": true, "schema": {"type": "string"}, "description": "Ko<PERSON> promosi"}], "responses": {"200": {"description": "Detail promosi ber<PERSON>il diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "code": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "value": {"type": "string"}, "min_booking_amount": {"type": "string"}, "valid_from": {"type": "string", "format": "date-time"}, "valid_until": {"type": "string", "format": "date-time"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/customer/promotions/validate": {"post": {"tags": ["Customer"], "summary": "Validasi kode promosi 🔵 CUSTOMER", "description": "Endpoint untuk memvalidasi kode promosi sebelum digunakan", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["code"], "properties": {"code": {"type": "string", "example": "WEEKEND20"}, "booking_amount": {"type": "number", "example": 100000}, "field_id": {"type": "integer", "example": 1}}}}}}, "responses": {"200": {"description": "Validasi promosi berhasil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"is_valid": {"type": "boolean", "example": true}, "discount_amount": {"type": "number", "example": 20000}, "final_amount": {"type": "number", "example": 80000}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/api/staff/kasir/payments": {"get": {"tags": ["Staff Kasir"], "summary": "Get daftar pembayaran untuk kasir 🟢 STAFF", "description": "Endpoint untuk mendapatkan semua pembayaran yang perlu diproses kasir", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "<PERSON><PERSON> halaman"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10}, "description": "Jumlah item per halaman"}, {"in": "query", "name": "status", "schema": {"type": "string", "enum": ["pending", "paid", "failed"]}, "description": "Filter berdasarkan status pembayaran"}, {"in": "query", "name": "method", "schema": {"type": "string", "enum": ["cash", "bank_transfer", "debit_card"]}, "description": "Filter berdasarkan metode pembayaran"}], "responses": {"200": {"description": "<PERSON>ftar pembayaran berhasil diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "payment_number": {"type": "string", "example": "PAY-********-001"}, "booking_id": {"type": "integer"}, "amount": {"type": "string"}, "method": {"type": "string"}, "status": {"type": "string"}}}}, "pagination": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/staff/kasir/payments/pending": {"get": {"tags": ["Staff Kasir"], "summary": "Get pembayaran pending 🟢 STAFF", "description": "Endpoint untuk mendapatkan daftar pembayaran yang masih pending", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10}}], "responses": {"200": {"description": "Daftar pembayaran pending ber<PERSON><PERSON> diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "payment_number": {"type": "string"}, "amount": {"type": "string"}, "method": {"type": "string"}, "status": {"type": "string", "example": "pending"}, "booking_id": {"type": "integer"}, "created_at": {"type": "string", "format": "date-time"}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/staff/kasir/payments/{id}": {"get": {"tags": ["Staff Kasir"], "summary": "Get detail pembayaran 🟢 STAFF", "description": "Endpoint untuk mendapatkan detail pembayaran berdasarkan ID untuk kasir", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID pembayaran"}], "responses": {"200": {"description": "Detail pembayaran berhasil diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"id": {"type": "integer"}, "payment_number": {"type": "string"}, "booking_id": {"type": "integer"}, "amount": {"type": "string"}, "method": {"type": "string"}, "status": {"type": "string"}, "customer_name": {"type": "string"}, "field_name": {"type": "string"}, "booking_date": {"type": "string", "format": "date"}, "created_at": {"type": "string", "format": "date-time"}, "confirmed_at": {"type": "string", "format": "date-time"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/staff/kasir/payments/manual": {"post": {"tags": ["Staff Kasir"], "summary": "Proses pembayaran manual 🟢 STAFF", "description": "Endpoint untuk memproses pembayaran manual (cash, transfer)", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["booking_id", "method", "amount"], "properties": {"booking_id": {"type": "integer", "example": 1, "description": "ID booking"}, "method": {"type": "string", "enum": ["cash", "bank_transfer", "debit_card"], "description": "<PERSON><PERSON>"}, "amount": {"type": "string", "example": "200000.00", "description": "<PERSON><PERSON><PERSON>"}, "reference_number": {"type": "string", "example": "TRF123456", "description": "Nomor referensi (untuk non-cash)"}, "notes": {"type": "string", "description": "Catatan pembayaran"}}}}}}, "responses": {"201": {"description": "Pembayaran berhasil diproses", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Payment processed successfully"}, "data": {"type": "object", "properties": {"payment_number": {"type": "string", "example": "PAY-********-001"}, "amount": {"type": "string"}, "method": {"type": "string"}, "status": {"type": "string"}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/staff/kasir/payments/{id}/confirm": {"put": {"tags": ["Staff Kasir"], "summary": "Konfirmasi pembayaran 🟢 STAFF", "description": "Endpoint untuk mengkonfirmasi pembayaran yang sudah diterima", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID pembayaran yang akan dikonfirmasi"}], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "properties": {"notes": {"type": "string", "example": "Pembayaran cash diterima", "description": "Catatan konfirmasi pembayaran"}}}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Payment confirmed successfully"}, "data": {"type": "object", "properties": {"id": {"type": "integer"}, "status": {"type": "string", "example": "completed"}, "confirmed_at": {"type": "string", "format": "date-time"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/staff/kasir/statistics": {"get": {"tags": ["Staff Kasir"], "summary": "Get statistik pembayaran 🟢 STAFF", "description": "Endpoint untuk mendapatkan statistik pembayaran untuk kasir", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "date_from", "schema": {"type": "string", "format": "date"}, "description": "Filter tanggal mulai"}, {"in": "query", "name": "date_to", "schema": {"type": "string", "format": "date"}, "description": "Filter tanggal akhir"}], "responses": {"200": {"description": "Statistik pembayaran berhasil diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"total_payments": {"type": "integer", "example": 150}, "total_amount": {"type": "string", "example": "15000000.00"}, "completed_payments": {"type": "integer", "example": 140}, "pending_payments": {"type": "integer", "example": 10}, "payment_methods": {"type": "object", "properties": {"cash": {"type": "integer"}, "transfer": {"type": "integer"}, "ewallet": {"type": "integer"}}}, "daily_revenue": {"type": "array", "items": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "amount": {"type": "string"}}}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/staff/kasir/daily-report": {"get": {"tags": ["Staff Kasir"], "summary": "Get daily cash report 🟢 STAFF", "description": "Endpoint untuk mendapatkan laporan kas harian untuk kasir", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "date", "schema": {"type": "string", "format": "date"}, "description": "<PERSON><PERSON> laporan (default hari ini)"}], "responses": {"200": {"description": "Daily cash report berhasil diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "opening_balance": {"type": "string", "example": "1000000.00"}, "total_cash_in": {"type": "string", "example": "2500000.00"}, "total_cash_out": {"type": "string", "example": "500000.00"}, "closing_balance": {"type": "string", "example": "3000000.00"}, "transactions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "type": {"type": "string", "enum": ["cash_in", "cash_out"]}, "amount": {"type": "string"}, "description": {"type": "string"}, "time": {"type": "string", "format": "time"}}}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/staff/kasir/dashboard": {"get": {"tags": ["Staff Kasir"], "summary": "Get dashboard kasir 🟢 STAFF", "description": "Endpoint untuk mendapatkan dashboard kasir dengan statistik pembayaran", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "responses": {"200": {"description": "Dashboard kasir ber<PERSON><PERSON> di<PERSON>bil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"staff_info": {"type": "object", "properties": {"name": {"type": "string", "example": "<PERSON>"}, "employee_id": {"type": "string", "example": "KS001"}, "role": {"type": "string", "example": "<PERSON><PERSON>"}}}, "today_summary": {"type": "object", "properties": {"total_transactions": {"type": "integer", "example": 15}, "total_amount": {"type": "string", "example": "3500000.00"}, "pending_payments": {"type": "integer", "example": 3}, "cash_payments": {"type": "integer", "example": 8}, "digital_payments": {"type": "integer", "example": 7}}}, "recent_transactions": {"type": "array", "items": {"type": "object", "properties": {"payment_number": {"type": "string"}, "amount": {"type": "string"}, "method": {"type": "string"}, "status": {"type": "string"}}}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/staff/kasir/payment-methods": {"get": {"tags": ["Staff Kasir"], "summary": "Get metode pembayaran yang tersedia 🟢 STAFF", "description": "Endpoint untuk mendapatkan daftar metode pembayaran yang dapat diproses kasir", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "responses": {"200": {"description": "Daftar metode pembayaran berhasil diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"manual_methods": {"type": "array", "items": {"type": "object", "properties": {"value": {"type": "string", "example": "cash"}, "label": {"type": "string", "example": "Cash"}, "description": {"type": "string", "example": "Pembayaran tunai"}, "requires_reference": {"type": "boolean", "example": false}}}}, "digital_methods": {"type": "array", "items": {"type": "object", "properties": {"value": {"type": "string", "example": "ewallet"}, "label": {"type": "string", "example": "E-Wallet"}, "description": {"type": "string", "example": "GoPay, OVO, DANA, dll"}, "gateway": {"type": "string", "example": "midtrans"}}}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/staff/operator/dashboard": {"get": {"tags": ["Staff Operator"], "summary": "Get dashboard operator lapangan 🟢 STAFF", "description": "Endpoint untuk mendapatkan dashboard operator dengan statistik dan informasi lapangan yang di<PERSON>ola", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "responses": {"200": {"description": "Dashboard operator be<PERSON><PERSON><PERSON> <PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"operator_info": {"type": "object", "properties": {"name": {"type": "string", "example": "<PERSON>"}, "employee_id": {"type": "string", "example": "OP001"}, "assigned_fields": {"type": "integer", "example": 3}}}, "today_stats": {"type": "object", "properties": {"total_bookings": {"type": "integer", "example": 12}, "pending_confirmations": {"type": "integer", "example": 3}, "active_bookings": {"type": "integer", "example": 2}}}, "field_status": {"type": "array", "items": {"type": "object", "properties": {"field_name": {"type": "string"}, "status": {"type": "string"}, "current_booking": {"type": "object"}}}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/staff/operator/fields": {"get": {"tags": ["Staff Operator"], "summary": "Get lapangan yang ditugaskan 🟢 STAFF", "description": "Endpoint untuk mendapatkan daftar lapangan yang ditugaskan ke operator", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> lapangan ber<PERSON><PERSON> di<PERSON>bil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "type": {"type": "string"}, "status": {"type": "string"}, "current_bookings": {"type": "integer"}, "today_revenue": {"type": "string"}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/staff/operator/bookings/{id}/confirm": {"put": {"tags": ["Staff Operator"], "summary": "Konfirmasi booking 🟢 STAFF", "description": "Endpoint untuk mengkonfirmasi booking yang pending", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID booking yang akan dikon<PERSON><PERSON>si"}], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "properties": {"notes": {"type": "string", "example": "Booking di<PERSON><PERSON><PERSON><PERSON>, lapangan siap", "description": "Catatan konfirmasi (opsional)"}}}}}}, "responses": {"200": {"description": "Booking ber<PERSON><PERSON><PERSON><PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Booking confirmed successfully"}, "data": {"type": "object", "properties": {"booking_id": {"type": "integer"}, "status": {"type": "string", "example": "confirmed"}, "confirmed_at": {"type": "string", "format": "date-time"}, "confirmed_by": {"type": "string"}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/staff/operator/bookings/{id}/complete": {"put": {"tags": ["Staff Operator"], "summary": "Complete booking 🟢 STAFF", "description": "Endpoint untuk menyelesaikan booking set<PERSON>h selesai dimainkan", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID booking yang akan diselesaikan"}], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "properties": {"notes": {"type": "string", "example": "Booking selesai dengan baik"}, "rating": {"type": "integer", "minimum": 1, "maximum": 5, "example": 5}}}}}}, "responses": {"200": {"description": "Booking ber<PERSON><PERSON> diseles<PERSON>kan", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Booking completed successfully"}, "data": {"type": "object", "properties": {"id": {"type": "integer"}, "status": {"type": "string", "example": "completed"}, "completed_at": {"type": "string", "format": "date-time"}, "completed_by": {"type": "string"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/staff/operator/bookings": {"get": {"tags": ["Staff Operator"], "summary": "Get semua booking untuk operator 🟢 STAFF", "description": "Endpoint untuk mendapatkan semua booking yang ditangani operator (hanya lapangan yang ditugaskan)", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10}}, {"in": "query", "name": "status", "schema": {"type": "string", "enum": ["pending", "confirmed", "completed", "cancelled"]}, "description": "Filter berdasarkan status booking"}, {"in": "query", "name": "field_id", "schema": {"type": "integer"}, "description": "<PERSON><PERSON> be<PERSON><PERSON><PERSON>n"}, {"in": "query", "name": "date_from", "schema": {"type": "string", "format": "date"}, "description": "Filter tanggal mulai"}, {"in": "query", "name": "date_to", "schema": {"type": "string", "format": "date"}, "description": "Filter tanggal akhir"}], "responses": {"200": {"description": "Daftar booking ber<PERSON><PERSON> diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "booking_number": {"type": "string"}, "field_name": {"type": "string"}, "customer_name": {"type": "string"}, "date": {"type": "string", "format": "date"}, "start_time": {"type": "string"}, "end_time": {"type": "string"}, "status": {"type": "string"}, "total_amount": {"type": "string"}}}}, "pagination": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/staff/operator/bookings/pending": {"get": {"tags": ["Staff Operator"], "summary": "Get pending bookings 🟢 STAFF", "description": "Endpoint untuk mendapatkan booking yang pending untuk operator", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "responses": {"200": {"description": "Pending bookings ber<PERSON><PERSON> diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "booking_number": {"type": "string"}, "field_name": {"type": "string"}, "customer_name": {"type": "string"}, "date": {"type": "string", "format": "date"}, "start_time": {"type": "string", "format": "time"}, "end_time": {"type": "string", "format": "time"}, "status": {"type": "string", "example": "pending"}, "created_at": {"type": "string", "format": "date-time"}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/staff/supervisor/dashboard": {"get": {"tags": ["Staff Supervisor"], "summary": "Get dashboard supervisor 🔴 SUPERVISOR ONLY", "description": "Endpoint untuk mendapatkan dashboard supervisor dengan system overview", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "responses": {"200": {"description": "Dashboard supervisor <PERSON><PERSON><PERSON><PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"system_overview": {"type": "object", "properties": {"total_users": {"type": "integer", "example": 1500}, "active_sessions": {"type": "integer", "example": 45}, "system_uptime": {"type": "string", "example": "15 days, 8 hours"}, "database_status": {"type": "string", "example": "healthy"}}}, "business_metrics": {"type": "object", "properties": {"total_revenue": {"type": "string"}, "total_bookings": {"type": "integer"}, "system_performance": {"type": "object"}}}, "recent_activities": {"type": "array", "items": {"type": "object", "properties": {"activity": {"type": "string"}, "user": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}}}}, "alerts": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string"}, "message": {"type": "string"}, "severity": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/staff/supervisor/system-health": {"get": {"tags": ["Staff Supervisor"], "summary": "Get system health 🔴 SUPERVISOR ONLY", "description": "Endpoint untuk mendapatkan status kesehatan sistem dan monitoring\n\n**🔐 ACCESS LEVEL:**\n- ✅ **Supervisor Sistem** (supervisor_sistem) ONLY\n- ❌ Manager dan staff lainnya TIDAK dapat mengakses\n", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "responses": {"200": {"description": "System health berhasil diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"system_status": {"type": "string", "example": "healthy"}, "uptime": {"type": "string", "example": "15 days, 8 hours"}, "memory_usage": {"type": "object", "properties": {"used": {"type": "string", "example": "256 MB"}, "total": {"type": "string", "example": "512 MB"}, "percentage": {"type": "number", "example": 50}}}, "database_status": {"type": "string", "example": "connected"}, "active_connections": {"type": "integer", "example": 25}, "response_time": {"type": "string", "example": "45ms"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"description": "Forbidden - Hanya Supervisor yang dapat menga<PERSON>es", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Access denied - Supervisor level required"}}}}}}}}}, "/api/staff/supervisor/staff": {"post": {"tags": ["Staff Supervisor"], "summary": "Create staff user 🔴 SUPERVISOR ONLY", "description": "Endpoint untuk membuat user staff baru\n\n**🔐 ACCESS LEVEL:**\n- ✅ **Supervisor Sistem** (supervisor_sistem) ONLY\n- ❌ Manager dan staff lainnya TIDAK dapat mengakses\n", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name", "email", "password", "phone", "role"], "properties": {"name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "minLength": 6, "example": "password123"}, "phone": {"type": "string", "example": "081234567890"}, "role": {"type": "string", "enum": ["staff_kasir", "operator_lapangan", "manajer_futsal"], "example": "staff_kasir"}, "department": {"type": "string", "example": "Operations"}, "employee_id": {"type": "string", "example": "EMP001"}}}}}}, "responses": {"201": {"description": "Staff user ber<PERSON><PERSON> dibuat", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Staff user created successfully"}, "data": {"$ref": "#/components/schemas/User"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"description": "Forbidden - Hanya Supervisor yang dapat membuat staff", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Access denied - Supervisor level required"}}}}}}}}}, "/api/staff/supervisor/users": {"get": {"tags": ["Staff Supervisor"], "summary": "Get semua users 🔴 SUPERVISOR ONLY", "description": "Endpoint untuk mendapatkan daftar semua users dengan full access\n\n**🔐 ACCESS LEVEL:**\n- ✅ **Supervisor Sistem** (supervisor_sistem) ONLY\n- ❌ Manager dan staff lainnya TIDAK dapat mengakses\n", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "Halaman data"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10}, "description": "Jumlah data per halaman"}, {"in": "query", "name": "role", "schema": {"type": "string", "enum": ["pengunjung", "penyewa", "staff_kasir", "operator_lapangan", "manajer_futsal", "supervisor_sistem"]}, "description": "<PERSON><PERSON> be<PERSON> role"}, {"in": "query", "name": "search", "schema": {"type": "string"}, "description": "<PERSON><PERSON><PERSON> berda<PERSON><PERSON> nama atau email"}, {"in": "query", "name": "is_active", "schema": {"type": "boolean"}, "description": "Filter berdasarkan status aktif"}, {"in": "query", "name": "department", "schema": {"type": "string"}, "description": "<PERSON>lter berda<PERSON>kan departemen (untuk staff)"}], "responses": {"200": {"description": "Daftar users berhasil diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"users": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}, "summary": {"type": "object", "properties": {"total_users": {"type": "integer"}, "active_users": {"type": "integer"}, "role_breakdown": {"type": "object"}}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"description": "Forbidden - Hanya Supervisor yang dapat menga<PERSON>es", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Access denied - Supervisor level required"}}}}}}}}}, "/api/staff/supervisor/audit-logs": {"get": {"tags": ["Staff Supervisor"], "summary": "Get audit logs 🔴 SUPERVISOR ONLY", "description": "Endpoint untuk mendapatkan audit logs sistem\n\n**🔐 ACCESS LEVEL:**\n- ✅ **Supervisor Sistem** (supervisor_sistem) ONLY\n- ❌ Manager dan staff lainnya TIDAK dapat mengakses\n", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "Halaman data"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 50}, "description": "Jumlah data per halaman"}, {"in": "query", "name": "action", "schema": {"type": "string"}, "description": "Filter berdasarkan action"}, {"in": "query", "name": "user_id", "schema": {"type": "integer"}, "description": "Filter berdasarkan user ID"}, {"in": "query", "name": "date_from", "schema": {"type": "string", "format": "date"}, "description": "Filter tanggal mulai"}, {"in": "query", "name": "date_to", "schema": {"type": "string", "format": "date"}, "description": "Filter tanggal akhir"}], "responses": {"200": {"description": "Audit logs berhasil diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"logs": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "user_id": {"type": "integer"}, "action": {"type": "string"}, "table_name": {"type": "string"}, "record_id": {"type": "integer"}, "old_values": {"type": "object"}, "new_values": {"type": "object"}, "ip_address": {"type": "string"}, "user_agent": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}}}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"description": "Forbidden - Hanya Supervisor yang dapat menga<PERSON>es", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Access denied - Supervisor level required"}}}}}}}}}, "/api/staff/supervisor/system-config": {"get": {"tags": ["Staff Supervisor"], "summary": "Get system configuration 🔴 SUPERVISOR ONLY", "description": "Endpoint untuk mendapatkan konfigurasi sistem\n\n**🔐 ACCESS LEVEL:**\n- ✅ **Supervisor Sistem** (supervisor_sistem) ONLY\n- ❌ Manager dan staff lainnya TIDAK dapat mengakses\n", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "responses": {"200": {"description": "System configuration berhasil diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"app_name": {"type": "string", "example": "Enhanced Futsal Booking System"}, "version": {"type": "string", "example": "2.0.0"}, "environment": {"type": "string", "example": "production"}, "database": {"type": "object", "properties": {"host": {"type": "string"}, "port": {"type": "integer"}, "name": {"type": "string"}}}, "features": {"type": "object", "properties": {"auto_completion": {"type": "boolean"}, "notifications": {"type": "boolean"}, "audit_logs": {"type": "boolean"}}}, "limits": {"type": "object", "properties": {"max_bookings_per_day": {"type": "integer"}, "max_concurrent_users": {"type": "integer"}}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"description": "Forbidden - Hanya Supervisor yang dapat menga<PERSON>es", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Access denied - Supervisor level required"}}}}}}}}}, "/api/staff/supervisor/system-maintenance": {"post": {"tags": ["Staff Supervisor"], "summary": "Trigger system maintenance 🔴 SUPERVISOR ONLY", "description": "Endpoint untuk memicu tugas maintenance sistem\n\n**🔐 ACCESS LEVEL:**\n- ✅ **Supervisor Sistem** (supervisor_sistem) ONLY\n- ❌ Manager dan staff lainnya TIDAK dapat mengakses\n", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["task"], "properties": {"task": {"type": "string", "enum": ["cleanup_logs", "optimize_database", "clear_cache", "backup_data"], "example": "cleanup_logs"}, "options": {"type": "object", "properties": {"days_to_keep": {"type": "integer", "example": 30}, "force": {"type": "boolean", "example": false}}}}}}}}, "responses": {"200": {"description": "Maintenance task ber<PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Maintenance task completed successfully"}, "data": {"type": "object", "properties": {"task": {"type": "string"}, "started_at": {"type": "string", "format": "date-time"}, "completed_at": {"type": "string", "format": "date-time"}, "results": {"type": "object"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"description": "Forbidden - Hanya Supervisor yang dapat menga<PERSON>es", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Access denied - Supervisor level required"}}}}}}}}}, "/api/staff/supervisor/database-stats": {"get": {"tags": ["Staff Supervisor"], "summary": "Get database statistics 🔴 SUPERVISOR ONLY", "description": "Endpoint untuk mendapatkan statistik database yang detail\n\n**🔐 ACCESS LEVEL:**\n- ✅ **Supervisor Sistem** (supervisor_sistem) ONLY\n- ❌ Manager dan staff lainnya TIDAK dapat mengakses\n", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "responses": {"200": {"description": "Database statistics berhasil diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"database_info": {"type": "object", "properties": {"name": {"type": "string"}, "version": {"type": "string"}, "size": {"type": "string"}}}, "table_stats": {"type": "array", "items": {"type": "object", "properties": {"table_name": {"type": "string"}, "row_count": {"type": "integer"}, "size": {"type": "string"}}}}, "performance": {"type": "object", "properties": {"active_connections": {"type": "integer"}, "slow_queries": {"type": "integer"}, "cache_hit_ratio": {"type": "number"}}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"description": "Forbidden - Hanya Supervisor yang dapat menga<PERSON>es", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Access denied - Supervisor level required"}}}}}}}}}, "/api/staff/supervisor/error-logs": {"get": {"tags": ["Staff Supervisor"], "summary": "Get system error logs 🔴 SUPERVISOR ONLY", "description": "Endpoint untuk mendapatkan log error sistem\n\n**🔐 ACCESS LEVEL:**\n- ✅ **Supervisor Sistem** (supervisor_sistem) ONLY\n- ❌ Manager dan staff lainnya TIDAK dapat mengakses\n", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "Halaman data"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 50}, "description": "Jumlah data per halaman"}, {"in": "query", "name": "level", "schema": {"type": "string", "enum": ["error", "warning", "info"], "default": "error"}, "description": "Level log yang ingin diambil"}], "responses": {"200": {"description": "Error logs berhasil diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"logs": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "level": {"type": "string"}, "message": {"type": "string"}, "stack": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "source": {"type": "string"}}}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"description": "Forbidden - Hanya Supervisor yang dapat menga<PERSON>es", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Access denied - Supervisor level required"}}}}}}}}}, "/api/admin/settings": {"get": {"tags": ["Admin"], "summary": "Get semua system settings 🟡 MANAGEMENT", "description": "Endpoint untuk mendapatkan semua pengaturan sistem", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "responses": {"200": {"description": "Success response", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string"}, "value": {"type": "string"}, "description": {"type": "string"}, "is_public": {"type": "boolean"}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/admin/audit-logs": {"get": {"tags": ["Admin"], "summary": "Get semua audit logs 🟡 MANAGEMENT", "description": "Endpoint untuk mendapatkan semua log audit sistem", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10}}, {"in": "query", "name": "user_id", "schema": {"type": "integer"}, "description": "Filter berdasarkan user ID"}, {"in": "query", "name": "action", "schema": {"type": "string"}, "description": "Filter berdasarkan action (CREATE, UPDATE, DELETE)"}, {"in": "query", "name": "table_name", "schema": {"type": "string"}, "description": "Filter berdasarkan nama tabel"}, {"in": "query", "name": "date_from", "schema": {"type": "string", "format": "date"}, "description": "Filter tanggal mulai"}, {"in": "query", "name": "date_to", "schema": {"type": "string", "format": "date"}, "description": "Filter tanggal akhir"}], "responses": {"200": {"description": "Daftar audit logs ber<PERSON><PERSON> diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "user_id": {"type": "integer"}, "action": {"type": "string"}, "table_name": {"type": "string"}, "record_id": {"type": "integer"}, "old_values": {"type": "object"}, "new_values": {"type": "object"}, "created_at": {"type": "string", "format": "date-time"}}}}, "pagination": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/admin/notifications": {"get": {"tags": ["Admin"], "summary": "Get semua notifikasi 🟡 MANAGEMENT", "description": "Endpoint untuk mendapatkan semua notifikasi sistem", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10}}, {"in": "query", "name": "user_id", "schema": {"type": "integer"}, "description": "Filter berdasarkan user ID"}, {"in": "query", "name": "type", "schema": {"type": "string"}, "description": "Filter berdasarkan tipe notifikasi"}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> notif<PERSON> ber<PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "title": {"type": "string"}, "message": {"type": "string"}, "type": {"type": "string"}, "priority": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}}}}, "pagination": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}, "post": {"tags": ["Admin"], "summary": "Buat notifikasi sistem 🟡 MANAGEMENT", "description": "Endpoint untuk membuat notifikasi sistem baru", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["title", "message"], "properties": {"title": {"type": "string", "example": "Maintenance Scheduled"}, "message": {"type": "string", "example": "System maintenance will be performed tonight"}, "user_id": {"type": "integer", "example": 123}, "type": {"type": "string", "example": "system"}, "priority": {"type": "string", "enum": ["low", "medium", "high", "urgent"], "example": "high"}}}}}}, "responses": {"201": {"description": "Notifikasi berhasil dibuat", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Notification created successfully"}, "data": {"type": "object", "properties": {"id": {"type": "integer"}, "title": {"type": "string"}, "message": {"type": "string"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/admin/promotions": {"get": {"tags": ["Admin"], "summary": "Get semua promosi 🟡 MANAGEMENT", "description": "Endpoint untuk mendapatkan semua promosi untuk management", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10}}, {"in": "query", "name": "status", "schema": {"type": "string", "enum": ["active", "inactive", "all"]}, "description": "Filter berdasarkan status promosi"}], "responses": {"200": {"description": "Daftar promosi ber<PERSON><PERSON> di<PERSON>bil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "code": {"type": "string"}, "type": {"type": "string"}, "value": {"type": "string"}, "is_active": {"type": "boolean"}, "usage_count": {"type": "integer"}, "created_at": {"type": "string", "format": "date-time"}}}}, "pagination": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}, "post": {"tags": ["Admin"], "summary": "Buat promosi baru 🟡 MANAGEMENT", "description": "Endpoint untuk membuat promosi baru", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name", "code", "type", "value"], "properties": {"name": {"type": "string", "example": "Weekend Discount"}, "code": {"type": "string", "example": "WEEKEND20"}, "description": {"type": "string", "example": "20% discount for weekend bookings"}, "type": {"type": "string", "enum": ["percentage", "fixed"], "example": "percentage"}, "value": {"type": "string", "example": "20.00"}, "min_booking_amount": {"type": "string", "example": "50000.00"}, "usage_limit": {"type": "integer", "example": 100}, "valid_from": {"type": "string", "format": "date-time"}, "valid_until": {"type": "string", "format": "date-time"}, "applicable_fields": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}}}}}}, "responses": {"201": {"description": "<PERSON>mosi berhasil dibuat", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Promotion created successfully"}, "data": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "code": {"type": "string"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/admin/promotions/{id}": {"put": {"tags": ["Admin"], "summary": "Update promosi 🟡 MANAGEMENT", "description": "Endpoint untuk mengupdate promosi\n\n**🔐 ACCESS LEVEL:**\n- ✅ **Supervisor Sistem** (supervisor_sistem)\n- ✅ **Manager Futsal** (manajer_futsal)\n- ❌ Staff lainnya tidak dapat mengakses\n", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID promosi yang akan diupdate"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "example": "Diskon Weekend Updated"}, "description": {"type": "string", "example": "Diskon khusus untuk booking weekend"}, "value": {"type": "number", "example": 15}, "min_amount": {"type": "number", "example": 100000}, "max_discount": {"type": "number", "example": 50000}, "usage_limit": {"type": "integer", "example": 100}, "user_limit": {"type": "integer", "example": 1}, "start_date": {"type": "string", "format": "date"}, "end_date": {"type": "string", "format": "date"}, "is_active": {"type": "boolean", "example": true}}}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> ber<PERSON>il diupdate", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Promotion updated successfully"}, "data": {"$ref": "#/components/schemas/Promotion"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/admin/users": {"get": {"tags": ["Admin"], "summary": "Get semua users 🟡 MANAGEMENT", "description": "Endpoint untuk mendapatkan semua users untuk management\n\n**🔐 ACCESS LEVEL:**\n- ✅ **Supervisor Sistem** (supervisor_sistem) - Dapat melihat SEMUA users\n- ✅ **Manager Futsal** (manajer_futsal) - <PERSON>ya dapat melihat users dengan role di BAWAH mereka\n- ❌ Staff lainnya tidak dapat mengakses\n\n**🛡️ ROLE HIERARCHY PROTECTION:**\n- Manager TIDAK dapat melihat/mengelola Supervisor\n- Setiap role hanya dapat mengelola role di bawah level mereka\n", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10}}, {"in": "query", "name": "role", "schema": {"type": "string", "enum": ["pengunjung", "penyewa", "staff_kasir", "operator_lapangan", "manajer_futsal", "supervisor_sistem"]}}, {"in": "query", "name": "search", "schema": {"type": "string"}, "description": "Search by name or email"}], "responses": {"200": {"description": "Success response", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}, "pagination": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/admin/users/{id}": {"get": {"tags": ["Admin"], "summary": "Get detail user 🟡 MANAGEMENT", "description": "Endpoint untuk mendapatkan detail user berdasarkan ID\n\n**🔐 ACCESS LEVEL:**\n- ✅ **Supervisor Sistem** (supervisor_sistem) - Dapat melihat SEMUA users\n- ✅ **Manager Futsal** (manajer_futsal) - <PERSON>ya dapat melihat users dengan role di BAWAH mereka\n- ❌ Staff lainnya tidak dapat mengakses\n\n**🛡️ ROLE HIERARCHY PROTECTION:**\n- Manager TIDAK dapat melihat detail Supervisor\n- Error 403 jika mencoba akses user dengan role lebih tinggi\n", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID user"}], "responses": {"200": {"description": "Detail user ber<PERSON><PERSON> diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/components/schemas/User"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}}}, "put": {"tags": ["Admin"], "summary": "Update user 🟡 MANAGEMENT", "description": "Endpoint untuk mengupdate data user", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID user"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "phone": {"type": "string", "example": "081234567890"}, "role": {"type": "string", "enum": ["pengunjung", "penyewa", "staff_kasir", "operator_lapangan", "manajer_futsal", "supervisor_sistem"], "example": "penyewa"}, "is_active": {"type": "boolean", "example": true}}}}}}, "responses": {"200": {"description": "User berhasil diupdate", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "User updated successfully"}, "data": {"$ref": "#/components/schemas/User"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}}}, "delete": {"tags": ["Admin"], "summary": "Deactivate user 🟡 MANAGEMENT", "description": "Endpoint untuk menonaktifkan user (soft delete)\n\n**🔐 ACCESS LEVEL:**\n- ✅ **Supervisor Sistem** (supervisor_sistem)\n- ✅ **Manager Futsal** (manajer_futsal)\n- ❌ Staff lainnya tidak dapat mengakses\n", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID user yang akan din<PERSON>"}], "responses": {"200": {"description": "User be<PERSON><PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "User deactivated successfully"}, "data": {"$ref": "#/components/schemas/User"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"description": "Forbidden - Hanya Management yang dapat menonaktifkan user", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Access denied - Management level required"}, "required_roles": {"type": "array", "items": {"type": "string"}, "example": ["manajer_futsal", "supervisor_sistem"]}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/admin/fields": {"get": {"tags": ["Admin"], "summary": "Get semua lapangan 🟡 MANAGEMENT", "description": "Endpoint untuk mendapatkan daftar semua lapangan dengan filter\n\n**🔐 ACCESS LEVEL:**\n- ✅ **Supervisor Sistem** (supervisor_sistem)\n- ✅ **Manager Futsal** (manajer_futsal)\n- ❌ Staff lainnya tidak dapat mengakses\n", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "Halaman data"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10}, "description": "Jumlah data per halaman"}, {"in": "query", "name": "status", "schema": {"type": "string", "enum": ["active", "inactive"]}, "description": "Filter berdasarkan status"}, {"in": "query", "name": "type", "schema": {"type": "string"}, "description": "Filter berdasarkan tipe lapangan"}, {"in": "query", "name": "location", "schema": {"type": "string"}, "description": "Filter berda<PERSON> lokasi"}, {"in": "query", "name": "search", "schema": {"type": "string"}, "description": "<PERSON><PERSON><PERSON> be<PERSON> nama lapangan"}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> lapangan ber<PERSON><PERSON> di<PERSON>bil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"fields": {"type": "array", "items": {"$ref": "#/components/schemas/Field"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}, "summary": {"type": "object", "properties": {"total_fields": {"type": "integer"}, "active_fields": {"type": "integer"}, "inactive_fields": {"type": "integer"}}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"description": "Forbidden - Hanya Management yang dapat mengakses", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Access denied - Management level required"}, "required_roles": {"type": "array", "items": {"type": "string"}, "example": ["manajer_futsal", "supervisor_sistem"]}}}}}}}}, "post": {"tags": ["Admin"], "summary": "Create lapangan baru 🟡 MANAGEMENT", "description": "Endpoint untuk membuat lapangan baru\n\n**🔐 ACCESS LEVEL:**\n- ✅ **Supervisor Sistem** (supervisor_sistem)\n- ✅ **Manager Futsal** (manajer_futsal)\n- ❌ Staff lainnya tidak dapat mengakses\n", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name", "price"], "properties": {"name": {"type": "string", "example": "Lapangan A"}, "type": {"type": "string", "example": "futsal"}, "description": {"type": "string", "example": "Lapangan futsal indoor dengan rumput sintetis"}, "price": {"type": "number", "example": 100000}, "capacity": {"type": "integer", "example": 22}, "location": {"type": "string", "example": "Lantai 1"}, "address": {"type": "string", "example": "Jl. Futsal No. 123"}, "facilities": {"type": "array", "items": {"type": "string"}, "example": ["AC", "Sound System", "Lighting"]}, "coordinates": {"type": "object", "properties": {"lat": {"type": "number"}, "lng": {"type": "number"}}}}}}}}, "responses": {"201": {"description": "Lapangan berhasil dibuat", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Field created successfully"}, "data": {"$ref": "#/components/schemas/Field"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"description": "Forbidden - Hanya Management yang dapat membuat lapangan", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Access denied - Management level required"}}}}}}}}}, "/api/admin/fields/{id}": {"get": {"tags": ["Admin"], "summary": "Get detail lapangan 🟡 MANAGEMENT", "description": "Endpoint untuk mendapatkan detail lapangan untuk admin\n\n**🔐 ACCESS LEVEL:**\n- ✅ **Supervisor Sistem** (supervisor_sistem)\n- ✅ **Manager Futsal** (manajer_futsal)\n- ❌ Staff lainnya tidak dapat mengakses\n", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID lapangan yang akan diambil"}], "responses": {"200": {"description": "Detail lapangan ber<PERSON>il diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/components/schemas/Field"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"description": "Forbidden - Hanya Management yang dapat mengakses", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Access denied - Management level required"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}}}, "put": {"tags": ["Admin"], "summary": "Update lapangan 🟡 MANAGEMENT", "description": "Endpoint untuk mengupdate data lapangan\n\n**🔐 ACCESS LEVEL:**\n- ✅ **Supervisor Sistem** (supervisor_sistem)\n- ✅ **Manager Futsal** (manajer_futsal)\n- ❌ Staff lainnya tidak dapat mengakses\n", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID lapangan yang akan diupdate"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "example": "Lapangan A Updated"}, "type": {"type": "string", "example": "futsal"}, "description": {"type": "string", "example": "Lapangan futsal indoor dengan fasilitas lengkap"}, "price": {"type": "number", "example": 120000}, "capacity": {"type": "integer", "example": 22}, "location": {"type": "string", "example": "Lantai 2"}, "address": {"type": "string", "example": "Jl. Futsal No. 123"}, "facilities": {"type": "array", "items": {"type": "string"}, "example": ["AC", "Sound System", "Lighting", "Parking"]}, "is_active": {"type": "boolean", "example": true}}}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> ber<PERSON>il diupdate", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Field updated successfully"}, "data": {"$ref": "#/components/schemas/Field"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"description": "Forbidden - Hanya Management yang dapat mengupdate lapangan", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Access denied - Management level required"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}}}, "delete": {"tags": ["Admin"], "summary": "Delete lapangan 🟡 MANAGEMENT", "description": "Endpoint untuk menghapus lapangan (soft delete)\n\n**🔐 ACCESS LEVEL:**\n- ✅ **Supervisor Sistem** (supervisor_sistem)\n- ✅ **Manager Futsal** (manajer_futsal)\n- ❌ Staff lainnya tidak dapat mengakses\n", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID lapangan yang akan dihapus"}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> ber<PERSON>il di<PERSON>pus (soft delete)", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Field deleted successfully"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Lapangan A"}, "is_active": {"type": "boolean", "example": false}, "deleted_at": {"type": "string", "format": "date-time"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"description": "Forbidden - Hanya Management yang dapat menghapus lapangan", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Access denied - Management level required"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/admin/bookings": {"get": {"tags": ["Admin"], "summary": "Get semua booking untuk admin 🟡 MANAGEMENT", "description": "Endpoint untuk mendapatkan semua booking dengan akses admin/management", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "<PERSON><PERSON> halaman"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10}, "description": "Jumlah item per halaman"}, {"in": "query", "name": "status", "schema": {"type": "string", "enum": ["pending", "confirmed", "completed", "cancelled"]}, "description": "Filter berdasarkan status booking"}, {"in": "query", "name": "user_id", "schema": {"type": "integer"}, "description": "Filter berdasarkan user ID"}, {"in": "query", "name": "field_id", "schema": {"type": "integer"}, "description": "Filter berdasarkan field ID"}, {"in": "query", "name": "date_from", "schema": {"type": "string", "format": "date"}, "description": "Filter tanggal mulai"}, {"in": "query", "name": "date_to", "schema": {"type": "string", "format": "date"}, "description": "Filter tanggal akhir"}], "responses": {"200": {"description": "Daftar booking ber<PERSON><PERSON> diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Booking"}}, "pagination": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/admin/auto-completion/trigger": {"post": {"tags": ["Admin"], "summary": "Trigger auto-completion manual 🔴 SUPERVISOR ONLY", "description": "Endpoint untuk memicu proses auto-completion booking secara manual (supervisor only)", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "responses": {"200": {"description": "Auto-completion ber<PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Auto-completion process triggered successfully"}, "data": {"type": "object", "properties": {"processed_bookings": {"type": "integer", "example": 5}, "completed_bookings": {"type": "integer", "example": 3}, "failed_bookings": {"type": "integer", "example": 0}, "execution_time": {"type": "string", "example": "2.5s"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/enhanced/": {"get": {"tags": ["Enhanced Features"], "summary": "Get enhanced features overview ⚪ PUBLIC", "description": "Endpoint untuk mendapatkan overview fitur-fitur enhanced sistem", "responses": {"200": {"description": "Overview fitur enhanced berhasil diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Enhanced Futsal Booking System - Advanced Features"}, "data": {"type": "object", "properties": {"version": {"type": "string", "example": "2.0.0"}, "features": {"type": "array", "items": {"type": "string"}, "example": ["Auto-completion", "Real-time notifications", "Advanced analytics", "Role-based access"]}, "status": {"type": "string", "example": "active"}, "last_updated": {"type": "string", "format": "date-time"}}}}}}}}}}}, "/api/enhanced/features": {"get": {"tags": ["Enhanced Features"], "summary": "Get detailed enhanced features ⚪ PUBLIC", "description": "Endpoint untuk mendapatkan daftar detail fitur-fitur enhanced", "responses": {"200": {"description": "<PERSON>ftar fitur enhanced berhasil diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"booking_features": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "status": {"type": "string"}}}, "example": [{"name": "Auto-completion", "description": "Automatic booking completion system", "status": "active"}]}, "notification_features": {"type": "array", "items": {"type": "object"}}, "analytics_features": {"type": "array", "items": {"type": "object"}}, "security_features": {"type": "array", "items": {"type": "object"}}}}}}}}}}}}, "/api/staff/manager/dashboard": {"get": {"tags": ["Staff Manager"], "summary": "Get dashboard manager 🟡 MANAGEMENT", "description": "Endpoint untuk mendapatkan dashboard manager dengan business metrics", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "responses": {"200": {"description": "Dashboard manager <PERSON><PERSON><PERSON><PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"overview": {"type": "object", "properties": {"total_revenue": {"type": "string", "example": "50000000.00"}, "total_bookings": {"type": "integer", "example": 1250}, "active_customers": {"type": "integer", "example": 350}, "field_utilization": {"type": "number", "example": 75.5}}}, "today_stats": {"type": "object", "properties": {"revenue": {"type": "string"}, "bookings": {"type": "integer"}, "completed_bookings": {"type": "integer"}}}, "monthly_trends": {"type": "array", "items": {"type": "object", "properties": {"month": {"type": "string"}, "revenue": {"type": "string"}, "bookings": {"type": "integer"}}}}, "top_fields": {"type": "array", "items": {"type": "object", "properties": {"field_name": {"type": "string"}, "revenue": {"type": "string"}, "booking_count": {"type": "integer"}}}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/staff/manager/users": {"get": {"tags": ["Staff Manager"], "summary": "Get semua users untuk manager 🟡 MANAGEMENT", "description": "Endpoint untuk mendapatkan daftar semua users dengan filter", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "Halaman data"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10}, "description": "Jumlah data per halaman"}, {"in": "query", "name": "role", "schema": {"type": "string", "enum": ["pengunjung", "penyewa", "staff_kasir", "operator_lapangan", "manajer_futsal", "supervisor_sistem"]}, "description": "<PERSON><PERSON> be<PERSON> role"}, {"in": "query", "name": "search", "schema": {"type": "string"}, "description": "<PERSON><PERSON><PERSON> berda<PERSON><PERSON> nama atau email"}, {"in": "query", "name": "is_active", "schema": {"type": "boolean"}, "description": "Filter berdasarkan status aktif"}], "responses": {"200": {"description": "Daftar users berhasil diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"users": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/staff/manager/fields": {"get": {"tags": ["Staff Manager"], "summary": "Get semua lapangan untuk manager 🟡 MANAGEMENT", "description": "Endpoint untuk mendapatkan daftar semua lapangan", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> lapangan ber<PERSON><PERSON> di<PERSON>bil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Lapangan A"}, "type": {"type": "string", "example": "Futsal Indoor"}, "capacity": {"type": "integer", "example": 10}, "price_per_hour": {"type": "string", "example": "100000.00"}, "is_active": {"type": "boolean", "example": true}, "rating": {"type": "number", "example": 4.5}, "total_bookings": {"type": "integer", "example": 150}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/staff/manager/bookings": {"get": {"tags": ["Staff Manager"], "summary": "Get semua booking untuk manager 🟡 MANAGEMENT", "description": "Endpoint untuk mendapatkan daftar semua booking dengan filter\n\n**🔐 ACCESS LEVEL:**\n- ✅ **Supervisor Sistem** (supervisor_sistem)\n- ✅ **Manager Futsal** (manajer_futsal)\n- ❌ Staff lainnya tidak dapat mengakses\n", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "Halaman data"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10}, "description": "Jumlah data per halaman"}, {"in": "query", "name": "status", "schema": {"type": "string", "enum": ["pending", "confirmed", "completed", "cancelled"]}, "description": "Filter berdasarkan status booking"}, {"in": "query", "name": "field_id", "schema": {"type": "integer"}, "description": "<PERSON>lter berda<PERSON><PERSON> lapangan"}, {"in": "query", "name": "date_from", "schema": {"type": "string", "format": "date"}, "description": "Filter tanggal mulai"}, {"in": "query", "name": "date_to", "schema": {"type": "string", "format": "date"}, "description": "Filter tanggal akhir"}], "responses": {"200": {"description": "Daftar booking ber<PERSON><PERSON> diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"bookings": {"type": "array", "items": {"$ref": "#/components/schemas/Booking"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}, "summary": {"type": "object", "properties": {"total_bookings": {"type": "integer"}, "total_revenue": {"type": "string"}, "status_breakdown": {"type": "object"}}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/staff/manager/analytics": {"get": {"tags": ["Staff Manager"], "summary": "Get business analytics 🟡 MANAGEMENT", "description": "Endpoint untuk mendapatkan business analytics untuk manager", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "date_from", "schema": {"type": "string", "format": "date"}, "description": "Filter tanggal mulai"}, {"in": "query", "name": "date_to", "schema": {"type": "string", "format": "date"}, "description": "Filter tanggal akhir"}, {"in": "query", "name": "period", "schema": {"type": "string", "enum": ["daily", "weekly", "monthly", "yearly"], "default": "monthly"}, "description": "Periode analisis"}], "responses": {"200": {"description": "Business analytics ber<PERSON><PERSON> diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"revenue_analytics": {"type": "object", "properties": {"total_revenue": {"type": "string"}, "growth_rate": {"type": "number"}, "period_comparison": {"type": "object"}}}, "booking_analytics": {"type": "object", "properties": {"total_bookings": {"type": "integer"}, "completion_rate": {"type": "number"}, "cancellation_rate": {"type": "number"}}}, "customer_analytics": {"type": "object", "properties": {"new_customers": {"type": "integer"}, "returning_customers": {"type": "integer"}, "customer_retention": {"type": "number"}}}, "field_performance": {"type": "array", "items": {"type": "object", "properties": {"field_name": {"type": "string"}, "utilization_rate": {"type": "number"}, "revenue": {"type": "string"}}}}, "trends": {"type": "array", "items": {"type": "object", "properties": {"period": {"type": "string"}, "revenue": {"type": "string"}, "bookings": {"type": "integer"}}}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/staff/manager/reports/revenue": {"get": {"tags": ["Staff Manager"], "summary": "Get revenue report 🟡 MANAGEMENT", "description": "Endpoint untuk mendapatkan laporan revenue dengan filter tanggal\n\n**🔐 ACCESS LEVEL:**\n- ✅ **Supervisor Sistem** (supervisor_sistem)\n- ✅ **Manager Futsal** (manajer_futsal)\n- ❌ Staff lainnya tidak dapat mengakses\n", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "date_from", "schema": {"type": "string", "format": "date"}, "description": "Filter tanggal mulai"}, {"in": "query", "name": "date_to", "schema": {"type": "string", "format": "date"}, "description": "Filter tanggal akhir"}], "responses": {"200": {"description": "Revenue report ber<PERSON>il diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"total_revenue": {"type": "string", "example": "15000000.00"}, "period": {"type": "object", "properties": {"from": {"type": "string", "format": "date"}, "to": {"type": "string", "format": "date"}}}, "daily_breakdown": {"type": "array", "items": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "revenue": {"type": "string"}, "bookings_count": {"type": "integer"}}}}, "payment_methods": {"type": "object", "properties": {"cash": {"type": "string"}, "transfer": {"type": "string"}, "qris": {"type": "string"}}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/staff/manager/reports/bookings": {"get": {"tags": ["Staff Manager"], "summary": "Get bookings report 🟡 MANAGEMENT", "description": "Endpoint untuk mendapatkan laporan booking dengan filter tanggal\n\n**🔐 ACCESS LEVEL:**\n- ✅ **Supervisor Sistem** (supervisor_sistem)\n- ✅ **Manager Futsal** (manajer_futsal)\n- ❌ Staff lainnya tidak dapat mengakses\n", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "date_from", "schema": {"type": "string", "format": "date"}, "description": "Filter tanggal mulai"}, {"in": "query", "name": "date_to", "schema": {"type": "string", "format": "date"}, "description": "Filter tanggal akhir"}], "responses": {"200": {"description": "Bookings report ber<PERSON><PERSON> diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"total_bookings": {"type": "integer", "example": 150}, "period": {"type": "object", "properties": {"from": {"type": "string", "format": "date"}, "to": {"type": "string", "format": "date"}}}, "status_breakdown": {"type": "object", "properties": {"completed": {"type": "integer"}, "pending": {"type": "integer"}, "cancelled": {"type": "integer"}}}, "field_utilization": {"type": "array", "items": {"type": "object", "properties": {"field_name": {"type": "string"}, "bookings_count": {"type": "integer"}, "utilization_rate": {"type": "number"}}}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}, "/api/staff/manager/staff-performance": {"get": {"tags": ["Staff Manager"], "summary": "Get staff performance 🟡 MANAGEMENT", "description": "Endpoint untuk mendapatkan metrics performa staff\n\n**🔐 ACCESS LEVEL:**\n- ✅ **Supervisor Sistem** (supervisor_sistem)\n- ✅ **Manager Futsal** (manajer_futsal)\n- ❌ Staff lainnya tidak dapat mengakses\n", "security": [{"bearerAuth": []}, {"cookieAuth": []}], "parameters": [{"in": "query", "name": "date_from", "schema": {"type": "string", "format": "date"}, "description": "Filter tanggal mulai"}, {"in": "query", "name": "date_to", "schema": {"type": "string", "format": "date"}, "description": "Filter tanggal akhir"}], "responses": {"200": {"description": "Staff performance metrics ber<PERSON><PERSON> diambil", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"period": {"type": "object", "properties": {"start_date": {"type": "string", "format": "date"}, "end_date": {"type": "string", "format": "date"}}}, "staff_performance": {"type": "object", "properties": {"kasir": {"type": "object", "properties": {"total_transactions": {"type": "integer", "example": 150}, "total_amount": {"type": "string", "example": "15000000.00"}, "average_processing_time": {"type": "number", "example": 2.5}}}, "operator": {"type": "object", "properties": {"total_bookings_handled": {"type": "integer", "example": 200}, "completion_rate": {"type": "number", "example": 95.5}, "customer_satisfaction": {"type": "number", "example": 4.8}}}}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}}}}, "components": {}, "tags": []}