import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import AuthProvider from "./contexts/AuthProvider";

// Layouts
import CustomerLayout from "./layouts/CustomerLayout";
import StaffLayout from "./layouts/StaffLayout";

// Auth Pages
import Login from "./pages/auth/Login";
import Register from "./pages/auth/Register";

// Customer Pages (penyewa)
import CustomerDashboard from "./pages/customer/Dashboard";
import BookingForm from "./pages/customer/Booking/BookingForm";
import BookingList from "./pages/customer/Booking/BookingList";
import FieldList from "./pages/customer/Field/FieldList";
import PaymentPage from "./pages/customer/Payment/PaymentPage";
import ProfilePage from "./pages/customer/Profile/ProfilePage";

// Staff Pages (kasir, operator, manager, supervisor)
import StaffDashboard from "./pages/staff/Dashboard";
import BookingManagement from "./pages/staff/Booking/BookingManagement";
import FieldManagement from "./pages/staff/Field/FieldManagement";
import PaymentManagement from "./pages/staff/Payment/PaymentManagement";
import UserManagement from "./pages/staff/User/UserManagement";

// Protected Route Wrapper
import ProtectedRoute from "./components/ProtectedRoute";

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* Public Routes */}
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />

          {/* CUSTOMER ROUTES (penyewa) */}
          <Route
            path="/"
            element={
              <ProtectedRoute allowedRoles={["penyewa"]}>
                <CustomerLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<CustomerDashboard />} />
            <Route path="bookings/new" element={<BookingForm />} />
            <Route path="bookings" element={<BookingList />} />
            <Route path="fields" element={<FieldList />} />
            <Route path="payments" element={<PaymentPage />} />
            <Route path="profile" element={<ProfilePage />} />
          </Route>

          {/* STAFF ROUTES (kasir, operator, manager, supervisor) */}
          <Route
            path="/staff"
            element={
              <ProtectedRoute allowedRoles={["staff_kasir", "operator_lapangan", "manajer_futsal", "supervisor_sistem"]}>
                <StaffLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<StaffDashboard />} />
            <Route path="bookings" element={<BookingManagement />} />
            <Route path="fields" element={<FieldManagement />} />
            <Route path="payments" element={<PaymentManagement />} />
            <Route path="users" element={<UserManagement />} />
          </Route>

          {/* Redirect unknown route */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
