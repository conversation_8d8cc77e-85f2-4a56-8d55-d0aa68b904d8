{"critical": [{"endpoint": "GET /api/customer/bookings/:bookingId/can-review", "template": "/**\n * @swagger\n * /api/customer/bookings/{bookingId}/can-review:\n *   get:\n *     tags: [Customer]\n *     summary: GET can-review\n *     description: Endpoint untuk get can-review\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: bookingId\n         required: true\n         schema:\n           type: integer\n         description: bookingId parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/customer/bookings/:id", "template": "/**\n * @swagger\n * /api/customer/bookings/{id}:\n *   get:\n *     tags: [Customer]\n *     summary: GET :id\n *     description: Endpoint untuk get :id\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/public/fields/:fieldId/promotions", "template": "/**\n * @swagger\n * /api/public/fields/{fieldId}/promotions:\n *   get:\n *     tags: [Public]\n *     summary: GET promotions\n *     description: Endpoint untuk get promotions\n\n     parameters:\n       - in: path\n         name: fieldId\n         required: true\n         schema:\n           type: integer\n         description: fieldId parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n\n */"}, {"endpoint": "GET /api/public/fields/:fieldId/rating", "template": "/**\n * @swagger\n * /api/public/fields/{fieldId}/rating:\n *   get:\n *     tags: [Public]\n *     summary: GET rating\n *     description: Endpoint untuk get rating\n\n     parameters:\n       - in: path\n         name: fieldId\n         required: true\n         schema:\n           type: integer\n         description: fieldId parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n\n */"}, {"endpoint": "GET /api/public/fields/:fieldId/reviews", "template": "/**\n * @swagger\n * /api/public/fields/{fieldId}/reviews:\n *   get:\n *     tags: [Public]\n *     summary: GET reviews\n *     description: Endpoint untuk get reviews\n\n     parameters:\n       - in: path\n         name: fieldId\n         required: true\n         schema:\n           type: integer\n         description: fieldId parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n\n */"}, {"endpoint": "GET /api/public/fields/:id", "template": "/**\n * @swagger\n * /api/public/fields/{id}:\n *   get:\n *     tags: [Public]\n *     summary: GET :id\n *     description: Endpoint untuk get :id\n\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n\n */"}, {"endpoint": "GET /api/public/fields/:id/availability", "template": "/**\n * @swagger\n * /api/public/fields/{id}/availability:\n *   get:\n *     tags: [Public]\n *     summary: GET availability\n *     description: Endpoint untuk get availability\n\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n\n */"}, {"endpoint": "PUT /api/customer/bookings/:id/cancel", "template": "/**\n * @swagger\n * /api/customer/bookings/{id}/cancel:\n *   put:\n *     tags: [Customer]\n *     summary: PUT cancel\n *     description: Endpoint untuk put cancel\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "PUT /api/customer/profile", "template": "/**\n * @swagger\n * /api/customer/profile:\n *   put:\n *     tags: [Customer]\n *     summary: PUT profile\n *     description: Endpoint untuk put profile\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}], "high": [{"endpoint": "DELETE /api/customer/favorites/:fieldId", "template": "/**\n * @swagger\n * /api/customer/favorites/{fieldId}:\n *   delete:\n *     tags: [Customer]\n *     summary: DELETE :fieldId\n *     description: Endpoint untuk delete :fieldId\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: fieldId\n         required: true\n         schema:\n           type: integer\n         description: fieldId parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "DELETE /api/customer/notifications/:id", "template": "/**\n * @swagger\n * /api/customer/notifications/{id}:\n *   delete:\n *     tags: [Customer]\n *     summary: DELETE :id\n *     description: Endpoint untuk delete :id\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/bookings/:id", "template": "/**\n * @swagger\n * /api/admin/bookings/{id}:\n *   get:\n *     tags: [Admin]\n *     summary: GET :id\n *     description: Endpoint untuk get :id\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/bookings/statistics", "template": "/**\n * @swagger\n * /api/admin/bookings/statistics:\n *   get:\n *     tags: [Admin]\n *     summary: GET statistics\n *     description: Endpoint untuk get statistics\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/auth/hash-password/:password", "template": "/**\n * @swagger\n * /api/auth/hash-password/{password}:\n *   get:\n *     tags: [Authentication]\n *     summary: GET :password\n *     description: Endpoint untuk get :password\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: password\n         required: true\n         schema:\n           type: integer\n         description: password parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/auth/health", "template": "/**\n * @swagger\n * /api/auth/health:\n *   get:\n *     tags: [Authentication]\n *     summary: GET health\n *     description: Endpoint untuk get health\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/auth/reset-password/:token", "template": "/**\n * @swagger\n * /api/auth/reset-password/{token}:\n *   get:\n *     tags: [Authentication]\n *     summary: GET :token\n *     description: Endpoint untuk get :token\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: token\n         required: true\n         schema:\n           type: integer\n         description: token parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/auth/verification-status/:email", "template": "/**\n * @swagger\n * /api/auth/verification-status/{email}:\n *   get:\n *     tags: [Authentication]\n *     summary: GET :email\n *     description: Endpoint untuk get :email\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: email\n         required: true\n         schema:\n           type: integer\n         description: email parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/auth/verify", "template": "/**\n * @swagger\n * /api/auth/verify:\n *   get:\n *     tags: [Authentication]\n *     summary: GET verify\n *     description: Endpoint untuk get verify\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/customer/favorites/:fieldId/check", "template": "/**\n * @swagger\n * /api/customer/favorites/{fieldId}/check:\n *   get:\n *     tags: [Customer]\n *     summary: GET check\n *     description: Endpoint untuk get check\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: fieldId\n         required: true\n         schema:\n           type: integer\n         description: fieldId parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/customer/favorites/availability", "template": "/**\n * @swagger\n * /api/customer/favorites/availability:\n *   get:\n *     tags: [Customer]\n *     summary: GET availability\n *     description: Endpoint untuk get availability\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/customer/favorites/count", "template": "/**\n * @swagger\n * /api/customer/favorites/count:\n *   get:\n *     tags: [Customer]\n *     summary: GET count\n *     description: Endpoint untuk get count\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/customer/favorites/statistics", "template": "/**\n * @swagger\n * /api/customer/favorites/statistics:\n *   get:\n *     tags: [Customer]\n *     summary: GET statistics\n *     description: Endpoint untuk get statistics\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/customer/notifications/count", "template": "/**\n * @swagger\n * /api/customer/notifications/count:\n *   get:\n *     tags: [Customer]\n *     summary: GET count\n *     description: Endpoint untuk get count\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/customer/notifications/statistics", "template": "/**\n * @swagger\n * /api/customer/notifications/statistics:\n *   get:\n *     tags: [Customer]\n *     summary: GET statistics\n *     description: Endpoint untuk get statistics\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/customer/notifications/unread-count", "template": "/**\n * @swagger\n * /api/customer/notifications/unread-count:\n *   get:\n *     tags: [Customer]\n *     summary: GET unread-count\n *     description: Endpoint untuk get unread-count\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/staff/kasir/payments/:id", "template": "/**\n * @swagger\n * /api/staff/kasir/payments/{id}:\n *   get:\n *     tags: [Staff Kasir]\n *     summary: GET :id\n *     description: Endpoint untuk get :id\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/staff/kasir/payments/pending", "template": "/**\n * @swagger\n * /api/staff/kasir/payments/pending:\n *   get:\n *     tags: [Staff Kasir]\n *     summary: GET pending\n *     description: Endpoint untuk get pending\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/staff/operator/bookings", "template": "/**\n * @swagger\n * /api/staff/operator/bookings:\n *   get:\n *     tags: [Staff Operator]\n *     summary: GET bookings\n *     description: Endpoint untuk get bookings\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/staff/operator/bookings/:id", "template": "/**\n * @swagger\n * /api/staff/operator/bookings/{id}:\n *   get:\n *     tags: [Staff Operator]\n *     summary: GET :id\n *     description: Endpoint untuk get :id\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/staff/operator/bookings/pending", "template": "/**\n * @swagger\n * /api/staff/operator/bookings/pending:\n *   get:\n *     tags: [Staff Operator]\n *     summary: GET pending\n *     description: Endpoint untuk get pending\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/auth/change-password", "template": "/**\n * @swagger\n * /api/auth/change-password:\n *   post:\n *     tags: [Authentication]\n *     summary: POST change-password\n *     description: Endpoint untuk post change-password\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/auth/forgot-password", "template": "/**\n * @swagger\n * /api/auth/forgot-password:\n *   post:\n *     tags: [Authentication]\n *     summary: POST forgot-password\n *     description: Endpoint untuk post forgot-password\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/auth/logout", "template": "/**\n * @swagger\n * /api/auth/logout:\n *   post:\n *     tags: [Authentication]\n *     summary: POST logout\n *     description: Endpoint untuk post logout\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/auth/refresh", "template": "/**\n * @swagger\n * /api/auth/refresh:\n *   post:\n *     tags: [Authentication]\n *     summary: POST refresh\n *     description: Endpoint untuk post refresh\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/auth/reset-password", "template": "/**\n * @swagger\n * /api/auth/reset-password:\n *   post:\n *     tags: [Authentication]\n *     summary: POST reset-password\n *     description: Endpoint untuk post reset-password\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/auth/send-verification", "template": "/**\n * @swagger\n * /api/auth/send-verification:\n *   post:\n *     tags: [Authentication]\n *     summary: POST send-verification\n *     description: Endpoint untuk post send-verification\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/auth/validate-email", "template": "/**\n * @swagger\n * /api/auth/validate-email:\n *   post:\n *     tags: [Authentication]\n *     summary: POST validate-email\n *     description: Endpoint untuk post validate-email\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/auth/validate-email-batch", "template": "/**\n * @swagger\n * /api/auth/validate-email-batch:\n *   post:\n *     tags: [Authentication]\n *     summary: POST validate-email-batch\n *     description: Endpoint untuk post validate-email-batch\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/auth/validate-email-quick", "template": "/**\n * @swagger\n * /api/auth/validate-email-quick:\n *   post:\n *     tags: [Authentication]\n *     summary: POST validate-email-quick\n *     description: Endpoint untuk post validate-email-quick\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/auth/verify-email", "template": "/**\n * @swagger\n * /api/auth/verify-email:\n *   post:\n *     tags: [Authentication]\n *     summary: POST verify-email\n *     description: Endpoint untuk post verify-email\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/customer/favorites/:fieldId", "template": "/**\n * @swagger\n * /api/customer/favorites/{fieldId}:\n *   post:\n *     tags: [Customer]\n *     summary: POST :fieldId\n *     description: Endpoint untuk post :fieldId\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: fieldId\n         required: true\n         schema:\n           type: integer\n         description: fieldId parameter\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/staff/kasir/payments/debug", "template": "/**\n * @swagger\n * /api/staff/kasir/payments/debug:\n *   post:\n *     tags: [Staff Kasir]\n *     summary: POST debug\n *     description: Endpoint untuk post debug\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "PUT /api/admin/bookings/:id/status", "template": "/**\n * @swagger\n * /api/admin/bookings/{id}/status:\n *   put:\n *     tags: [Admin]\n *     summary: PUT status\n *     description: Endpoint untuk put status\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "PUT /api/customer/favorites/:fieldId/toggle", "template": "/**\n * @swagger\n * /api/customer/favorites/{fieldId}/toggle:\n *   put:\n *     tags: [Customer]\n *     summary: PUT toggle\n *     description: Endpoint untuk put toggle\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: fieldId\n         required: true\n         schema:\n           type: integer\n         description: fieldId parameter\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "PUT /api/customer/notifications/:id/read", "template": "/**\n * @swagger\n * /api/customer/notifications/{id}/read:\n *   put:\n *     tags: [Customer]\n *     summary: PUT read\n *     description: Endpoint untuk put read\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "PUT /api/customer/notifications/read-all", "template": "/**\n * @swagger\n * /api/customer/notifications/read-all:\n *   put:\n *     tags: [Customer]\n *     summary: PUT read-all\n *     description: Endpoint untuk put read-all\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "PUT /api/staff/kasir/payments/:id/confirm", "template": "/**\n * @swagger\n * /api/staff/kasir/payments/{id}/confirm:\n *   put:\n *     tags: [Staff Kasir]\n *     summary: PUT confirm\n *     description: Endpoint untuk put confirm\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "PUT /api/staff/operator/bookings/:id/complete", "template": "/**\n * @swagger\n * /api/staff/operator/bookings/{id}/complete:\n *   put:\n *     tags: [Staff Operator]\n *     summary: PUT complete\n *     description: Endpoint untuk put complete\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "PUT /api/staff/operator/bookings/:id/confirm", "template": "/**\n * @swagger\n * /api/staff/operator/bookings/{id}/confirm:\n *   put:\n *     tags: [Staff Operator]\n *     summary: PUT confirm\n *     description: Endpoint untuk put confirm\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}], "medium": [{"endpoint": "DELETE /api/admin/audit-logs/cleanup", "template": "/**\n * @swagger\n * /api/admin/audit-logs/cleanup:\n *   delete:\n *     tags: [Admin]\n *     summary: DELETE cleanup\n *     description: Endpoint untuk delete cleanup\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "DELETE /api/admin/fields/:id", "template": "/**\n * @swagger\n * /api/admin/fields/{id}:\n *   delete:\n *     tags: [Admin]\n *     summary: DELETE :id\n *     description: Endpoint untuk delete :id\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "DELETE /api/admin/notifications/:id", "template": "/**\n * @swagger\n * /api/admin/notifications/{id}:\n *   delete:\n *     tags: [Admin]\n *     summary: DELETE :id\n *     description: Endpoint untuk delete :id\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "DELETE /api/admin/promotions/:id", "template": "/**\n * @swagger\n * /api/admin/promotions/{id}:\n *   delete:\n *     tags: [Admin]\n *     summary: DELETE :id\n *     description: Endpoint untuk delete :id\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "DELETE /api/admin/settings/:key", "template": "/**\n * @swagger\n * /api/admin/settings/{key}:\n *   delete:\n *     tags: [Admin]\n *     summary: DELETE :key\n *     description: Endpoint untuk delete :key\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: key\n         required: true\n         schema:\n           type: integer\n         description: key parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "DELETE /api/admin/users/:id", "template": "/**\n * @swagger\n * /api/admin/users/{id}:\n *   delete:\n *     tags: [Admin]\n *     summary: DELETE :id\n *     description: Endpoint untuk delete :id\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "DELETE /api/customer/reviews/:id", "template": "/**\n * @swagger\n * /api/customer/reviews/{id}:\n *   delete:\n *     tags: [Customer]\n *     summary: DELETE :id\n *     description: Endpoint untuk delete :id\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/", "template": "/**\n * @swagger\n * /api/:\n *   get:\n *     tags: [Other]\n *     summary: GET api\n *     description: Endpoint untuk get api\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/analytics/business", "template": "/**\n * @swagger\n * /api/admin/analytics/business:\n *   get:\n *     tags: [Admin]\n *     summary: GET business\n *     description: Endpoint untuk get business\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/analytics/performance", "template": "/**\n * @swagger\n * /api/admin/analytics/performance:\n *   get:\n *     tags: [Admin]\n *     summary: GET performance\n *     description: Endpoint untuk get performance\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/analytics/system", "template": "/**\n * @swagger\n * /api/admin/analytics/system:\n *   get:\n *     tags: [Admin]\n *     summary: GET system\n *     description: Endpoint untuk get system\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/audit-logs", "template": "/**\n * @swagger\n * /api/admin/audit-logs:\n *   get:\n *     tags: [Admin]\n *     summary: GET audit-logs\n *     description: Endpoint untuk get audit-logs\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/audit-logs/:id", "template": "/**\n * @swagger\n * /api/admin/audit-logs/{id}:\n *   get:\n *     tags: [Admin]\n *     summary: GET :id\n *     description: Endpoint untuk get :id\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/audit-logs/active-users", "template": "/**\n * @swagger\n * /api/admin/audit-logs/active-users:\n *   get:\n *     tags: [Admin]\n *     summary: GET active-users\n *     description: Endpoint untuk get active-users\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/audit-logs/export", "template": "/**\n * @swagger\n * /api/admin/audit-logs/export:\n *   get:\n *     tags: [Admin]\n *     summary: GET export\n *     description: Endpoint untuk get export\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/audit-logs/record/:tableName/:recordId", "template": "/**\n * @swagger\n * /api/admin/audit-logs/record/{tableName}/{recordId}:\n *   get:\n *     tags: [Admin]\n *     summary: GET :recordId\n *     description: Endpoint untuk get :recordId\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: tableName\n         required: true\n         schema:\n           type: integer\n         description: tableName parameter\n       - in: path\n         name: recordId\n         required: true\n         schema:\n           type: integer\n         description: recordId parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/audit-logs/statistics", "template": "/**\n * @swagger\n * /api/admin/audit-logs/statistics:\n *   get:\n *     tags: [Admin]\n *     summary: GET statistics\n *     description: Endpoint untuk get statistics\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/audit-logs/table/:tableName", "template": "/**\n * @swagger\n * /api/admin/audit-logs/table/{tableName}:\n *   get:\n *     tags: [Admin]\n *     summary: GET :tableName\n *     description: Endpoint untuk get :tableName\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: tableName\n         required: true\n         schema:\n           type: integer\n         description: tableName parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/audit-logs/user/:userId", "template": "/**\n * @swagger\n * /api/admin/audit-logs/user/{userId}:\n *   get:\n *     tags: [Admin]\n *     summary: GET :userId\n *     description: Endpoint untuk get :userId\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: userId\n         required: true\n         schema:\n           type: integer\n         description: userId parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/auto-completion/config", "template": "/**\n * @swagger\n * /api/admin/auto-completion/config:\n *   get:\n *     tags: [Admin]\n *     summary: GET config\n *     description: Endpoint untuk get config\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/auto-completion/eligible", "template": "/**\n * @swagger\n * /api/admin/auto-completion/eligible:\n *   get:\n *     tags: [Admin]\n *     summary: GET eligible\n *     description: Endpoint untuk get eligible\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/auto-completion/stats", "template": "/**\n * @swagger\n * /api/admin/auto-completion/stats:\n *   get:\n *     tags: [Admin]\n *     summary: GET stats\n *     description: Endpoint untuk get stats\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/fields", "template": "/**\n * @swagger\n * /api/admin/fields:\n *   get:\n *     tags: [Admin]\n *     summary: GET fields\n *     description: Endpoint untuk get fields\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/fields/:id", "template": "/**\n * @swagger\n * /api/admin/fields/{id}:\n *   get:\n *     tags: [Admin]\n *     summary: GET :id\n *     description: Endpoint untuk get :id\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/notifications", "template": "/**\n * @swagger\n * /api/admin/notifications:\n *   get:\n *     tags: [Admin]\n *     summary: GET notifications\n *     description: Endpoint untuk get notifications\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/notifications/:id/status", "template": "/**\n * @swagger\n * /api/admin/notifications/{id}/status:\n *   get:\n *     tags: [Admin]\n *     summary: GET status\n *     description: Endpoint untuk get status\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/notifications/statistics", "template": "/**\n * @swagger\n * /api/admin/notifications/statistics:\n *   get:\n *     tags: [Admin]\n *     summary: GET statistics\n *     description: Endpoint untuk get statistics\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/notifications/user/:userId", "template": "/**\n * @swagger\n * /api/admin/notifications/user/{userId}:\n *   get:\n *     tags: [Admin]\n *     summary: GET :userId\n *     description: Endpoint untuk get :userId\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: userId\n         required: true\n         schema:\n           type: integer\n         description: userId parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/promotions", "template": "/**\n * @swagger\n * /api/admin/promotions:\n *   get:\n *     tags: [Admin]\n *     summary: GET promotions\n *     description: Endpoint untuk get promotions\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/promotions/:id/usage", "template": "/**\n * @swagger\n * /api/admin/promotions/{id}/usage:\n *   get:\n *     tags: [Admin]\n *     summary: GET usage\n *     description: Endpoint untuk get usage\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/promotions/analytics", "template": "/**\n * @swagger\n * /api/admin/promotions/analytics:\n *   get:\n *     tags: [Admin]\n *     summary: GET analytics\n *     description: Endpoint untuk get analytics\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/role-management/dashboard", "template": "/**\n * @swagger\n * /api/admin/role-management/dashboard:\n *   get:\n *     tags: [Admin]\n *     summary: GET dashboard\n *     description: Endpoint untuk get dashboard\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/role-management/users", "template": "/**\n * @swagger\n * /api/admin/role-management/users:\n *   get:\n *     tags: [Admin]\n *     summary: GET users\n *     description: Endpoint untuk get users\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/settings", "template": "/**\n * @swagger\n * /api/admin/settings:\n *   get:\n *     tags: [Admin]\n *     summary: GET settings\n *     description: Endpoint untuk get settings\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/settings/:key", "template": "/**\n * @swagger\n * /api/admin/settings/{key}:\n *   get:\n *     tags: [Admin]\n *     summary: GET :key\n *     description: Endpoint untuk get :key\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: key\n         required: true\n         schema:\n           type: integer\n         description: key parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/settings/category/:category", "template": "/**\n * @swagger\n * /api/admin/settings/category/{category}:\n *   get:\n *     tags: [Admin]\n *     summary: GET :category\n *     description: Endpoint untuk get :category\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: category\n         required: true\n         schema:\n           type: integer\n         description: category parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/settings/public", "template": "/**\n * @swagger\n * /api/admin/settings/public:\n *   get:\n *     tags: [Admin]\n *     summary: GET public\n *     description: Endpoint untuk get public\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/users", "template": "/**\n * @swagger\n * /api/admin/users:\n *   get:\n *     tags: [Admin]\n *     summary: GET users\n *     description: Endpoint untuk get users\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/admin/users/:id", "template": "/**\n * @swagger\n * /api/admin/users/{id}:\n *   get:\n *     tags: [Admin]\n *     summary: GET :id\n *     description: Endpoint untuk get :id\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/auth/test-features", "template": "/**\n * @swagger\n * /api/auth/test-features:\n *   get:\n *     tags: [Authentication]\n *     summary: GET test-features\n *     description: Endpoint untuk get test-features\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/auth/test-smtp", "template": "/**\n * @swagger\n * /api/auth/test-smtp:\n *   get:\n *     tags: [Authentication]\n *     summary: GET test-smtp\n *     description: Endpoint untuk get test-smtp\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/customer/booking-history", "template": "/**\n * @swagger\n * /api/customer/booking-history:\n *   get:\n *     tags: [Customer]\n *     summary: GET booking-history\n *     description: Endpoint untuk get booking-history\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/customer/fields", "template": "/**\n * @swagger\n * /api/customer/fields:\n *   get:\n *     tags: [Customer]\n *     summary: GET fields\n *     description: Endpoint untuk get fields\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/customer/promotions", "template": "/**\n * @swagger\n * /api/customer/promotions:\n *   get:\n *     tags: [Customer]\n *     summary: GET promotions\n *     description: Endpoint untuk get promotions\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/customer/promotions/:code", "template": "/**\n * @swagger\n * /api/customer/promotions/{code}:\n *   get:\n *     tags: [Customer]\n *     summary: GET :code\n *     description: Endpoint untuk get :code\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: code\n         required: true\n         schema:\n           type: integer\n         description: code parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/customer/recommendations", "template": "/**\n * @swagger\n * /api/customer/recommendations:\n *   get:\n *     tags: [Customer]\n *     summary: GET recommendations\n *     description: Endpoint untuk get recommendations\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/customer/reviews", "template": "/**\n * @swagger\n * /api/customer/reviews:\n *   get:\n *     tags: [Customer]\n *     summary: GET reviews\n *     description: Endpoint untuk get reviews\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/customer/reviews/:id", "template": "/**\n * @swagger\n * /api/customer/reviews/{id}:\n *   get:\n *     tags: [Customer]\n *     summary: GET :id\n *     description: Endpoint untuk get :id\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/customer/upcoming-bookings", "template": "/**\n * @swagger\n * /api/customer/upcoming-bookings:\n *   get:\n *     tags: [Customer]\n *     summary: GET upcoming-bookings\n *     description: Endpoint untuk get upcoming-bookings\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/public/app-config", "template": "/**\n * @swagger\n * /api/public/app-config:\n *   get:\n *     tags: [Public]\n *     summary: GET app-config\n *     description: Endpoint untuk get app-config\n\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n\n */"}, {"endpoint": "GET /api/public/database-status", "template": "/**\n * @swagger\n * /api/public/database-status:\n *   get:\n *     tags: [Public]\n *     summary: GET database-status\n *     description: Endpoint untuk get database-status\n\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n\n */"}, {"endpoint": "GET /api/public/field-locations", "template": "/**\n * @swagger\n * /api/public/field-locations:\n *   get:\n *     tags: [Public]\n *     summary: GET field-locations\n *     description: Endpoint untuk get field-locations\n\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n\n */"}, {"endpoint": "GET /api/public/field-types", "template": "/**\n * @swagger\n * /api/public/field-types:\n *   get:\n *     tags: [Public]\n *     summary: GET field-types\n *     description: Endpoint untuk get field-types\n\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n\n */"}, {"endpoint": "GET /api/public/health", "template": "/**\n * @swagger\n * /api/public/health:\n *   get:\n *     tags: [Public]\n *     summary: GET health\n *     description: Endpoint untuk get health\n\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n\n */"}, {"endpoint": "GET /api/public/promotions", "template": "/**\n * @swagger\n * /api/public/promotions:\n *   get:\n *     tags: [Public]\n *     summary: GET promotions\n *     description: Endpoint untuk get promotions\n\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n\n */"}, {"endpoint": "GET /api/public/system-info", "template": "/**\n * @swagger\n * /api/public/system-info:\n *   get:\n *     tags: [Public]\n *     summary: GET system-info\n *     description: Endpoint untuk get system-info\n\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n\n */"}, {"endpoint": "GET /api/public/version", "template": "/**\n * @swagger\n * /api/public/version:\n *   get:\n *     tags: [Public]\n *     summary: GET version\n *     description: Endpoint untuk get version\n\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n\n */"}, {"endpoint": "GET /api/staff/kasir/bookings", "template": "/**\n * @swagger\n * /api/staff/kasir/bookings:\n *   get:\n *     tags: [Staff Kasir]\n *     summary: GET bookings\n *     description: Endpoint untuk get bookings\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/staff/kasir/bookings/:id", "template": "/**\n * @swagger\n * /api/staff/kasir/bookings/{id}:\n *   get:\n *     tags: [Staff Kasir]\n *     summary: GET :id\n *     description: Endpoint untuk get :id\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/staff/kasir/daily-report", "template": "/**\n * @swagger\n * /api/staff/kasir/daily-report:\n *   get:\n *     tags: [Staff Kasir]\n *     summary: GET daily-report\n *     description: Endpoint untuk get daily-report\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/staff/kasir/pending-payments", "template": "/**\n * @swagger\n * /api/staff/kasir/pending-payments:\n *   get:\n *     tags: [Staff Kasir]\n *     summary: GET pending-payments\n *     description: Endpoint untuk get pending-payments\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/staff/kasir/statistics", "template": "/**\n * @swagger\n * /api/staff/kasir/statistics:\n *   get:\n *     tags: [Staff Kasir]\n *     summary: GET statistics\n *     description: Endpoint untuk get statistics\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/staff/operator/booking-actions", "template": "/**\n * @swagger\n * /api/staff/operator/booking-actions:\n *   get:\n *     tags: [Staff Operator]\n *     summary: GET booking-actions\n *     description: Endpoint untuk get booking-actions\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/staff/operator/field-statuses", "template": "/**\n * @swagger\n * /api/staff/operator/field-statuses:\n *   get:\n *     tags: [Staff Operator]\n *     summary: GET field-statuses\n *     description: Endpoint untuk get field-statuses\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/staff/operator/fields", "template": "/**\n * @swagger\n * /api/staff/operator/fields:\n *   get:\n *     tags: [Staff Operator]\n *     summary: GET fields\n *     description: Endpoint untuk get fields\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/staff/operator/fields/:field_id/bookings", "template": "/**\n * @swagger\n * /api/staff/operator/fields/{field_id}/bookings:\n *   get:\n *     tags: [Staff Operator]\n *     summary: GET bookings\n *     description: Endpoint untuk get bookings\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: field_id\n         required: true\n         schema:\n           type: integer\n         description: field_id parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/staff/operator/schedule/:date", "template": "/**\n * @swagger\n * /api/staff/operator/schedule/{date}:\n *   get:\n *     tags: [Staff Operator]\n *     summary: GET :date\n *     description: Endpoint untuk get :date\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: date\n         required: true\n         schema:\n           type: integer\n         description: date parameter\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/staff/operator/schedule/today", "template": "/**\n * @swagger\n * /api/staff/operator/schedule/today:\n *   get:\n *     tags: [Staff Operator]\n *     summary: GET today\n *     description: Endpoint untuk get today\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/staff/operator/statistics", "template": "/**\n * @swagger\n * /api/staff/operator/statistics:\n *   get:\n *     tags: [Staff Operator]\n *     summary: GET statistics\n *     description: Endpoint untuk get statistics\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/staff/operator/today-schedule", "template": "/**\n * @swagger\n * /api/staff/operator/today-schedule:\n *   get:\n *     tags: [Staff Operator]\n *     summary: GET today-schedule\n *     description: Endpoint untuk get today-schedule\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/staff/supervisor/analytics", "template": "/**\n * @swagger\n * /api/staff/supervisor/analytics:\n *   get:\n *     tags: [Staff Supervisor]\n *     summary: GET analytics\n *     description: Endpoint untuk get analytics\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/staff/supervisor/audit-logs", "template": "/**\n * @swagger\n * /api/staff/supervisor/audit-logs:\n *   get:\n *     tags: [Staff Supervisor]\n *     summary: GET audit-logs\n *     description: Endpoint untuk get audit-logs\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/staff/supervisor/dashboard", "template": "/**\n * @swagger\n * /api/staff/supervisor/dashboard:\n *   get:\n *     tags: [Staff Supervisor]\n *     summary: GET dashboard\n *     description: Endpoint untuk get dashboard\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/staff/supervisor/database-stats", "template": "/**\n * @swagger\n * /api/staff/supervisor/database-stats:\n *   get:\n *     tags: [Staff Supervisor]\n *     summary: GET database-stats\n *     description: Endpoint untuk get database-stats\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/staff/supervisor/error-logs", "template": "/**\n * @swagger\n * /api/staff/supervisor/error-logs:\n *   get:\n *     tags: [Staff Supervisor]\n *     summary: GET error-logs\n *     description: Endpoint untuk get error-logs\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/staff/supervisor/system-config", "template": "/**\n * @swagger\n * /api/staff/supervisor/system-config:\n *   get:\n *     tags: [Staff Supervisor]\n *     summary: GET system-config\n *     description: Endpoint untuk get system-config\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/staff/supervisor/system-health", "template": "/**\n * @swagger\n * /api/staff/supervisor/system-health:\n *   get:\n *     tags: [Staff Supervisor]\n *     summary: GET system-health\n *     description: Endpoint untuk get system-health\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "GET /api/staff/supervisor/users", "template": "/**\n * @swagger\n * /api/staff/supervisor/users:\n *   get:\n *     tags: [Staff Supervisor]\n *     summary: GET users\n *     description: Endpoint untuk get users\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/admin/auto-completion/manual/:id", "template": "/**\n * @swagger\n * /api/admin/auto-completion/manual/{id}:\n *   post:\n *     tags: [Admin]\n *     summary: POST :id\n *     description: Endpoint untuk post :id\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/admin/fields", "template": "/**\n * @swagger\n * /api/admin/fields:\n *   post:\n *     tags: [Admin]\n *     summary: POST fields\n *     description: Endpoint untuk post fields\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/admin/notifications", "template": "/**\n * @swagger\n * /api/admin/notifications:\n *   post:\n *     tags: [Admin]\n *     summary: POST notifications\n *     description: Endpoint untuk post notifications\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/admin/notifications/broadcast", "template": "/**\n * @swagger\n * /api/admin/notifications/broadcast:\n *   post:\n *     tags: [Admin]\n *     summary: POST broadcast\n *     description: Endpoint untuk post broadcast\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/admin/promotions", "template": "/**\n * @swagger\n * /api/admin/promotions:\n *   post:\n *     tags: [Admin]\n *     summary: POST promotions\n *     description: Endpoint untuk post promotions\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/admin/role-management/request-change", "template": "/**\n * @swagger\n * /api/admin/role-management/request-change:\n *   post:\n *     tags: [Admin]\n *     summary: POST request-change\n *     description: Endpoint untuk post request-change\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/admin/settings", "template": "/**\n * @swagger\n * /api/admin/settings:\n *   post:\n *     tags: [Admin]\n *     summary: POST settings\n *     description: Endpoint untuk post settings\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/admin/settings/initialize", "template": "/**\n * @swagger\n * /api/admin/settings/initialize:\n *   post:\n *     tags: [Admin]\n *     summary: POST initialize\n *     description: Endpoint untuk post initialize\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/customer/promotions/apply", "template": "/**\n * @swagger\n * /api/customer/promotions/apply:\n *   post:\n *     tags: [Customer]\n *     summary: POST apply\n *     description: Endpoint untuk post apply\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/customer/promotions/calculate", "template": "/**\n * @swagger\n * /api/customer/promotions/calculate:\n *   post:\n *     tags: [Customer]\n *     summary: POST calculate\n *     description: Endpoint untuk post calculate\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/customer/promotions/validate", "template": "/**\n * @swagger\n * /api/customer/promotions/validate:\n *   post:\n *     tags: [Customer]\n *     summary: POST validate\n *     description: Endpoint untuk post validate\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/customer/reviews", "template": "/**\n * @swagger\n * /api/customer/reviews:\n *   post:\n *     tags: [Customer]\n *     summary: POST reviews\n *     description: Endpoint untuk post reviews\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/staff/supervisor/staff", "template": "/**\n * @swagger\n * /api/staff/supervisor/staff:\n *   post:\n *     tags: [Staff Supervisor]\n *     summary: POST staff\n *     description: Endpoint untuk post staff\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "POST /api/staff/supervisor/system-maintenance", "template": "/**\n * @swagger\n * /api/staff/supervisor/system-maintenance:\n *   post:\n *     tags: [Staff Supervisor]\n *     summary: POST system-maintenance\n *     description: Endpoint untuk post system-maintenance\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "PUT /api/admin/fields/:id", "template": "/**\n * @swagger\n * /api/admin/fields/{id}:\n *   put:\n *     tags: [Admin]\n *     summary: PUT :id\n *     description: Endpoint untuk put :id\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "PUT /api/admin/promotions/:id", "template": "/**\n * @swagger\n * /api/admin/promotions/{id}:\n *   put:\n *     tags: [Admin]\n *     summary: PUT :id\n *     description: Endpoint untuk put :id\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "PUT /api/admin/promotions/:id/toggle", "template": "/**\n * @swagger\n * /api/admin/promotions/{id}/toggle:\n *   put:\n *     tags: [Admin]\n *     summary: PUT toggle\n *     description: Endpoint untuk put toggle\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "PUT /api/admin/role-management/change-role", "template": "/**\n * @swagger\n * /api/admin/role-management/change-role:\n *   put:\n *     tags: [Admin]\n *     summary: PUT change-role\n *     description: Endpoint untuk put change-role\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "PUT /api/admin/settings/:key", "template": "/**\n * @swagger\n * /api/admin/settings/{key}:\n *   put:\n *     tags: [Admin]\n *     summary: PUT :key\n *     description: Endpoint untuk put :key\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: key\n         required: true\n         schema:\n           type: integer\n         description: key parameter\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "PUT /api/admin/settings/:key/reset", "template": "/**\n * @swagger\n * /api/admin/settings/{key}/reset:\n *   put:\n *     tags: [Admin]\n *     summary: PUT reset\n *     description: Endpoint untuk put reset\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: key\n         required: true\n         schema:\n           type: integer\n         description: key parameter\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "PUT /api/admin/settings/bulk-update", "template": "/**\n * @swagger\n * /api/admin/settings/bulk-update:\n *   put:\n *     tags: [Admin]\n *     summary: PUT bulk-update\n *     description: Endpoint untuk put bulk-update\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "PUT /api/admin/users/:id", "template": "/**\n * @swagger\n * /api/admin/users/{id}:\n *   put:\n *     tags: [Admin]\n *     summary: PUT :id\n *     description: Endpoint untuk put :id\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "PUT /api/customer/reviews/:id", "template": "/**\n * @swagger\n * /api/customer/reviews/{id}:\n *   put:\n *     tags: [Customer]\n *     summary: PUT :id\n *     description: Endpoint untuk put :id\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "PUT /api/staff/operator/fields/:id/status", "template": "/**\n * @swagger\n * /api/staff/operator/fields/{id}/status:\n *   put:\n *     tags: [Staff Operator]\n *     summary: PUT status\n *     description: Endpoint untuk put status\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}, {"endpoint": "PUT /api/staff/supervisor/users/:id/role", "template": "/**\n * @swagger\n * /api/staff/supervisor/users/{id}/role:\n *   put:\n *     tags: [Staff Supervisor]\n *     summary: PUT role\n *     description: Endpoint untuk put role\n     security:\n       - bearerAuth: []\n       - cookieAuth: []\n     parameters:\n       - in: path\n         name: id\n         required: true\n         schema:\n           type: integer\n         description: id parameter\n     requestBody:\n       required: true\n       content:\n         application/json:\n           schema:\n             type: object\n             properties:\n               # Add properties here\n               example_field:\n                 type: string\n                 example: \"example value\"\n *     responses:\n *       200:\n *         description: Success response\n *         content:\n *           application/json:\n *             schema:\n *               type: object\n *               properties:\n *                 success:\n *                   type: boolean\n *                   example: true\n *                 data:\n *                   type: object\n *                   # Add response schema here\n *       401:\n *         $ref: '#/components/responses/Unauthorized'\n */"}], "summary": {"critical_count": 9, "high_count": 40, "medium_count": 103, "total_undocumented": 152}}