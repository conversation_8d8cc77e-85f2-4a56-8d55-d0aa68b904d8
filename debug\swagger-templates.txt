// SWAGGER DOCUMENTATION TEMPLATES

// CRITICAL PRIORITY ENDPOINTS

// GET /api/customer/bookings/:bookingId/can-review
/**
 * @swagger
 * /api/customer/bookings/{bookingId}/can-review:
 *   get:
 *     tags: [Customer]
 *     summary: GET can-review
 *     description: Endpoint untuk get can-review
     security:
       - bearerAuth: []
       - cookieAuth: []
     parameters:
       - in: path
         name: bookingId
         required: true
         schema:
           type: integer
         description: bookingId parameter

 *     responses:
 *       200:
 *         description: Success response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   # Add response schema here
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */

// GET /api/customer/bookings/:id
/**
 * @swagger
 * /api/customer/bookings/{id}:
 *   get:
 *     tags: [Customer]
 *     summary: GET :id
 *     description: Endpoint untuk get :id
     security:
       - bearerAuth: []
       - cookieAuth: []
     parameters:
       - in: path
         name: id
         required: true
         schema:
           type: integer
         description: id parameter

 *     responses:
 *       200:
 *         description: Success response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   # Add response schema here
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */

// GET /api/public/fields/:fieldId/promotions
/**
 * @swagger
 * /api/public/fields/{fieldId}/promotions:
 *   get:
 *     tags: [Public]
 *     summary: GET promotions
 *     description: Endpoint untuk get promotions

     parameters:
       - in: path
         name: fieldId
         required: true
         schema:
           type: integer
         description: fieldId parameter

 *     responses:
 *       200:
 *         description: Success response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   # Add response schema here

 */

// GET /api/public/fields/:fieldId/rating
/**
 * @swagger
 * /api/public/fields/{fieldId}/rating:
 *   get:
 *     tags: [Public]
 *     summary: GET rating
 *     description: Endpoint untuk get rating

     parameters:
       - in: path
         name: fieldId
         required: true
         schema:
           type: integer
         description: fieldId parameter

 *     responses:
 *       200:
 *         description: Success response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   # Add response schema here

 */

// GET /api/public/fields/:fieldId/reviews
/**
 * @swagger
 * /api/public/fields/{fieldId}/reviews:
 *   get:
 *     tags: [Public]
 *     summary: GET reviews
 *     description: Endpoint untuk get reviews

     parameters:
       - in: path
         name: fieldId
         required: true
         schema:
           type: integer
         description: fieldId parameter

 *     responses:
 *       200:
 *         description: Success response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   # Add response schema here

 */

// GET /api/public/fields/:id
/**
 * @swagger
 * /api/public/fields/{id}:
 *   get:
 *     tags: [Public]
 *     summary: GET :id
 *     description: Endpoint untuk get :id

     parameters:
       - in: path
         name: id
         required: true
         schema:
           type: integer
         description: id parameter

 *     responses:
 *       200:
 *         description: Success response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   # Add response schema here

 */

// GET /api/public/fields/:id/availability
/**
 * @swagger
 * /api/public/fields/{id}/availability:
 *   get:
 *     tags: [Public]
 *     summary: GET availability
 *     description: Endpoint untuk get availability

     parameters:
       - in: path
         name: id
         required: true
         schema:
           type: integer
         description: id parameter

 *     responses:
 *       200:
 *         description: Success response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   # Add response schema here

 */

// PUT /api/customer/bookings/:id/cancel
/**
 * @swagger
 * /api/customer/bookings/{id}/cancel:
 *   put:
 *     tags: [Customer]
 *     summary: PUT cancel
 *     description: Endpoint untuk put cancel
     security:
       - bearerAuth: []
       - cookieAuth: []
     parameters:
       - in: path
         name: id
         required: true
         schema:
           type: integer
         description: id parameter
     requestBody:
       required: true
       content:
         application/json:
           schema:
             type: object
             properties:
               # Add properties here
               example_field:
                 type: string
                 example: "example value"
 *     responses:
 *       200:
 *         description: Success response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   # Add response schema here
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */

// PUT /api/customer/profile
/**
 * @swagger
 * /api/customer/profile:
 *   put:
 *     tags: [Customer]
 *     summary: PUT profile
 *     description: Endpoint untuk put profile
     security:
       - bearerAuth: []
       - cookieAuth: []

     requestBody:
       required: true
       content:
         application/json:
           schema:
             type: object
             properties:
               # Add properties here
               example_field:
                 type: string
                 example: "example value"
 *     responses:
 *       200:
 *         description: Success response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   # Add response schema here
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */


// HIGH PRIORITY ENDPOINTS

// DELETE /api/customer/favorites/:fieldId
/**
 * @swagger
 * /api/customer/favorites/{fieldId}:
 *   delete:
 *     tags: [Customer]
 *     summary: DELETE :fieldId
 *     description: Endpoint untuk delete :fieldId
     security:
       - bearerAuth: []
       - cookieAuth: []
     parameters:
       - in: path
         name: fieldId
         required: true
         schema:
           type: integer
         description: fieldId parameter

 *     responses:
 *       200:
 *         description: Success response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   # Add response schema here
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */

// DELETE /api/customer/notifications/:id
/**
 * @swagger
 * /api/customer/notifications/{id}:
 *   delete:
 *     tags: [Customer]
 *     summary: DELETE :id
 *     description: Endpoint untuk delete :id
     security:
       - bearerAuth: []
       - cookieAuth: []
     parameters:
       - in: path
         name: id
         required: true
         schema:
           type: integer
         description: id parameter

 *     responses:
 *       200:
 *         description: Success response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   # Add response schema here
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */

// GET /api/admin/bookings/:id
/**
 * @swagger
 * /api/admin/bookings/{id}:
 *   get:
 *     tags: [Admin]
 *     summary: GET :id
 *     description: Endpoint untuk get :id
     security:
       - bearerAuth: []
       - cookieAuth: []
     parameters:
       - in: path
         name: id
         required: true
         schema:
           type: integer
         description: id parameter

 *     responses:
 *       200:
 *         description: Success response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   # Add response schema here
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */

// GET /api/admin/bookings/statistics
/**
 * @swagger
 * /api/admin/bookings/statistics:
 *   get:
 *     tags: [Admin]
 *     summary: GET statistics
 *     description: Endpoint untuk get statistics
     security:
       - bearerAuth: []
       - cookieAuth: []


 *     responses:
 *       200:
 *         description: Success response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   # Add response schema here
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */

// GET /api/auth/hash-password/:password
/**
 * @swagger
 * /api/auth/hash-password/{password}:
 *   get:
 *     tags: [Authentication]
 *     summary: GET :password
 *     description: Endpoint untuk get :password
     security:
       - bearerAuth: []
       - cookieAuth: []
     parameters:
       - in: path
         name: password
         required: true
         schema:
           type: integer
         description: password parameter

 *     responses:
 *       200:
 *         description: Success response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   # Add response schema here
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */

// GET /api/auth/health
/**
 * @swagger
 * /api/auth/health:
 *   get:
 *     tags: [Authentication]
 *     summary: GET health
 *     description: Endpoint untuk get health
     security:
       - bearerAuth: []
       - cookieAuth: []


 *     responses:
 *       200:
 *         description: Success response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   # Add response schema here
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */

// GET /api/auth/reset-password/:token
/**
 * @swagger
 * /api/auth/reset-password/{token}:
 *   get:
 *     tags: [Authentication]
 *     summary: GET :token
 *     description: Endpoint untuk get :token
     security:
       - bearerAuth: []
       - cookieAuth: []
     parameters:
       - in: path
         name: token
         required: true
         schema:
           type: integer
         description: token parameter

 *     responses:
 *       200:
 *         description: Success response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   # Add response schema here
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */

// GET /api/auth/verification-status/:email
/**
 * @swagger
 * /api/auth/verification-status/{email}:
 *   get:
 *     tags: [Authentication]
 *     summary: GET :email
 *     description: Endpoint untuk get :email
     security:
       - bearerAuth: []
       - cookieAuth: []
     parameters:
       - in: path
         name: email
         required: true
         schema:
           type: integer
         description: email parameter

 *     responses:
 *       200:
 *         description: Success response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   # Add response schema here
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */

// GET /api/auth/verify
/**
 * @swagger
 * /api/auth/verify:
 *   get:
 *     tags: [Authentication]
 *     summary: GET verify
 *     description: Endpoint untuk get verify
     security:
       - bearerAuth: []
       - cookieAuth: []


 *     responses:
 *       200:
 *         description: Success response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   # Add response schema here
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */

// GET /api/customer/favorites/:fieldId/check
/**
 * @swagger
 * /api/customer/favorites/{fieldId}/check:
 *   get:
 *     tags: [Customer]
 *     summary: GET check
 *     description: Endpoint untuk get check
     security:
       - bearerAuth: []
       - cookieAuth: []
     parameters:
       - in: path
         name: fieldId
         required: true
         schema:
           type: integer
         description: fieldId parameter

 *     responses:
 *       200:
 *         description: Success response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   # Add response schema here
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */
