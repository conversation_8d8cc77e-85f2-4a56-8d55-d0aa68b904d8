# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Environment variables
.env
.env.local
.env.*.local
.env.development
.env.production

# Keep environment templates
!.env.example

# Logs
logs/
*.log
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE/Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# Temporary files
*.tmp
*.temp
*.bak
*.old

# Database files (local development)
*.sqlite
*.sqlite3
*.db

# Upload directories
uploads/
public/uploads/

# Build directories
build/
dist/

# Development and testing files (not needed in production)
jest.config.js
coverage/
database/
scripts/
postman/

# Development documentation (keep only essential docs)
docs/AUDIT_*.md
docs/BACKEND_*.md
docs/COMPREHENSIVE_*.md
docs/DEPLOYMENT_TEST_*.md
docs/END_TO_END_*.md
docs/PRODUCTION_READINESS_*.md
docs/PROGRESS_*.md
docs/ROUTES_*.md

# Testing files
*.test.js
test/
tests/
__tests__/

# Development scripts
scripts/
migrations/
seeds/

