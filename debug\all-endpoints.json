{"total_endpoints": 190, "by_file": {"routes/indexRoutes.js": [{"method": "GET", "path": "/", "file": "routes/indexRoutes.js", "fullPath": "/api/"}, {"method": "GET", "path": "/health", "file": "routes/indexRoutes.js", "fullPath": "/api/health"}, {"method": "GET", "path": "/routes", "file": "routes/indexRoutes.js", "fullPath": "/api/routes"}], "routes/testRoutes.js": [{"method": "GET", "path": "/health", "file": "routes/testRoutes.js", "fullPath": "/api/test/health"}, {"method": "GET", "path": "/routes", "file": "routes/testRoutes.js", "fullPath": "/api/test/routes"}, {"method": "GET", "path": "/database", "file": "routes/testRoutes.js", "fullPath": "/api/test/database"}, {"method": "GET", "path": "/auth", "file": "routes/testRoutes.js", "fullPath": "/api/test/auth"}, {"method": "GET", "path": "/public", "file": "routes/testRoutes.js", "fullPath": "/api/test/public"}, {"method": "GET", "path": "/customer", "file": "routes/testRoutes.js", "fullPath": "/api/test/customer"}, {"method": "GET", "path": "/admin", "file": "routes/testRoutes.js", "fullPath": "/api/test/admin"}, {"method": "GET", "path": "/staff", "file": "routes/testRoutes.js", "fullPath": "/api/test/staff"}, {"method": "GET", "path": "/environment", "file": "routes/testRoutes.js", "fullPath": "/api/test/environment"}, {"method": "GET", "path": "/memory", "file": "routes/testRoutes.js", "fullPath": "/api/test/memory"}], "routes/authRoutes.js": [{"method": "POST", "path": "/register", "file": "routes/authRoutes.js", "fullPath": "/api/auth/register"}, {"method": "POST", "path": "/login", "file": "routes/authRoutes.js", "fullPath": "/api/auth/login"}, {"method": "POST", "path": "/logout", "file": "routes/authRoutes.js", "fullPath": "/api/auth/logout"}, {"method": "GET", "path": "/profile", "file": "routes/authRoutes.js", "fullPath": "/api/auth/profile"}, {"method": "POST", "path": "/refresh", "file": "routes/authRoutes.js", "fullPath": "/api/auth/refresh"}, {"method": "GET", "path": "/roles", "file": "routes/authRoutes.js", "fullPath": "/api/auth/roles"}, {"method": "POST", "path": "/change-password", "file": "routes/authRoutes.js", "fullPath": "/api/auth/change-password"}, {"method": "GET", "path": "/verify", "file": "routes/authRoutes.js", "fullPath": "/api/auth/verify"}, {"method": "POST", "path": "/forgot-password", "file": "routes/authRoutes.js", "fullPath": "/api/auth/forgot-password"}, {"method": "GET", "path": "/reset-password/:token", "file": "routes/authRoutes.js", "fullPath": "/api/auth/reset-password/:token"}, {"method": "POST", "path": "/reset-password", "file": "routes/authRoutes.js", "fullPath": "/api/auth/reset-password"}, {"method": "POST", "path": "/send-verification", "file": "routes/authRoutes.js", "fullPath": "/api/auth/send-verification"}, {"method": "POST", "path": "/verify-email", "file": "routes/authRoutes.js", "fullPath": "/api/auth/verify-email"}, {"method": "GET", "path": "/verification-status/:email", "file": "routes/authRoutes.js", "fullPath": "/api/auth/verification-status/:email"}, {"method": "GET", "path": "/health", "file": "routes/authRoutes.js", "fullPath": "/api/auth/health"}, {"method": "GET", "path": "/test-features", "file": "routes/authRoutes.js", "fullPath": "/api/auth/test-features"}, {"method": "GET", "path": "/test-smtp", "file": "routes/authRoutes.js", "fullPath": "/api/auth/test-smtp"}, {"method": "GET", "path": "/hash-password/:password", "file": "routes/authRoutes.js", "fullPath": "/api/auth/hash-password/:password"}, {"method": "POST", "path": "/validate-email", "file": "routes/authRoutes.js", "fullPath": "/api/auth/validate-email"}, {"method": "POST", "path": "/validate-email-quick", "file": "routes/authRoutes.js", "fullPath": "/api/auth/validate-email-quick"}, {"method": "POST", "path": "/validate-email-batch", "file": "routes/authRoutes.js", "fullPath": "/api/auth/validate-email-batch"}], "routes/publicRoutes.js": [{"method": "GET", "path": "/system-info", "file": "routes/publicRoutes.js", "fullPath": "/api/public/system-info"}, {"method": "GET", "path": "/database-status", "file": "routes/publicRoutes.js", "fullPath": "/api/public/database-status"}, {"method": "GET", "path": "/fields", "file": "routes/publicRoutes.js", "fullPath": "/api/public/fields"}, {"method": "GET", "path": "/fields/:id", "file": "routes/publicRoutes.js", "fullPath": "/api/public/fields/:id"}, {"method": "GET", "path": "/fields/:id/availability", "file": "routes/publicRoutes.js", "fullPath": "/api/public/fields/:id/availability"}, {"method": "GET", "path": "/field-types", "file": "routes/publicRoutes.js", "fullPath": "/api/public/field-types"}, {"method": "GET", "path": "/field-locations", "file": "routes/publicRoutes.js", "fullPath": "/api/public/field-locations"}, {"method": "GET", "path": "/health", "file": "routes/publicRoutes.js", "fullPath": "/api/public/health"}, {"method": "GET", "path": "/version", "file": "routes/publicRoutes.js", "fullPath": "/api/public/version"}, {"method": "GET", "path": "/app-config", "file": "routes/publicRoutes.js", "fullPath": "/api/public/app-config"}, {"method": "GET", "path": "/fields/:fieldId/reviews", "file": "routes/publicRoutes.js", "fullPath": "/api/public/fields/:fieldId/reviews"}, {"method": "GET", "path": "/fields/:fieldId/rating", "file": "routes/publicRoutes.js", "fullPath": "/api/public/fields/:fieldId/rating"}, {"method": "GET", "path": "/promotions", "file": "routes/publicRoutes.js", "fullPath": "/api/public/promotions"}, {"method": "GET", "path": "/fields/:fieldId/promotions", "file": "routes/publicRoutes.js", "fullPath": "/api/public/fields/:fieldId/promotions"}, {"method": "GET", "path": "/debug/table/:tableName", "file": "routes/publicRoutes.js", "fullPath": "/api/public/debug/table/:tableName"}, {"method": "GET", "path": "/debug/test-settings", "file": "routes/publicRoutes.js", "fullPath": "/api/public/debug/test-settings"}, {"method": "GET", "path": "/debug/test-promotion", "file": "routes/publicRoutes.js", "fullPath": "/api/public/debug/test-promotion"}], "routes/customerRoutes.js": [{"method": "GET", "path": "/profile", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/profile"}, {"method": "PUT", "path": "/profile", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/profile"}, {"method": "GET", "path": "/fields", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/fields"}, {"method": "POST", "path": "/bookings", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/bookings"}, {"method": "GET", "path": "/bookings", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/bookings"}, {"method": "GET", "path": "/bookings/history", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/bookings/history"}, {"method": "GET", "path": "/bookings/:id", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/bookings/:id"}, {"method": "PUT", "path": "/bookings/:id/cancel", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/bookings/:id/cancel"}, {"method": "GET", "path": "/booking-history", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/booking-history"}, {"method": "GET", "path": "/upcoming-bookings", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/upcoming-bookings"}, {"method": "GET", "path": "/dashboard", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/dashboard"}, {"method": "GET", "path": "/notifications", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/notifications"}, {"method": "GET", "path": "/notifications/count", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/notifications/count"}, {"method": "GET", "path": "/notifications/unread-count", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/notifications/unread-count"}, {"method": "PUT", "path": "/notifications/:id/read", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/notifications/:id/read"}, {"method": "PUT", "path": "/notifications/read-all", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/notifications/read-all"}, {"method": "DELETE", "path": "/notifications/:id", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/notifications/:id"}, {"method": "GET", "path": "/notifications/statistics", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/notifications/statistics"}, {"method": "GET", "path": "/reviews", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/reviews"}, {"method": "POST", "path": "/reviews", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/reviews"}, {"method": "GET", "path": "/reviews/:id", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/reviews/:id"}, {"method": "PUT", "path": "/reviews/:id", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/reviews/:id"}, {"method": "DELETE", "path": "/reviews/:id", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/reviews/:id"}, {"method": "GET", "path": "/bookings/:bookingId/can-review", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/bookings/:bookingId/can-review"}, {"method": "GET", "path": "/favorites", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/favorites"}, {"method": "POST", "path": "/favorites/:fieldId", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/favorites/:fieldId"}, {"method": "DELETE", "path": "/favorites/:fieldId", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/favorites/:fieldId"}, {"method": "PUT", "path": "/favorites/:fieldId/toggle", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/favorites/:fieldId/toggle"}, {"method": "GET", "path": "/favorites/:fieldId/check", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/favorites/:fieldId/check"}, {"method": "GET", "path": "/favorites/availability", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/favorites/availability"}, {"method": "GET", "path": "/favorites/statistics", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/favorites/statistics"}, {"method": "GET", "path": "/favorites/count", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/favorites/count"}, {"method": "GET", "path": "/recommendations", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/recommendations"}, {"method": "GET", "path": "/promotions", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/promotions"}, {"method": "GET", "path": "/promotions/:code", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/promotions/:code"}, {"method": "POST", "path": "/promotions/validate", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/promotions/validate"}, {"method": "POST", "path": "/promotions/apply", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/promotions/apply"}, {"method": "POST", "path": "/promotions/calculate", "file": "routes/customerRoutes.js", "fullPath": "/api/customer/promotions/calculate"}], "routes/kasirRoutes.js": [{"method": "GET", "path": "/payments", "file": "routes/kasirRoutes.js", "fullPath": "/api/staff/kasir/payments"}, {"method": "GET", "path": "/payments/pending", "file": "routes/kasirRoutes.js", "fullPath": "/api/staff/kasir/payments/pending"}, {"method": "GET", "path": "/payments/:id", "file": "routes/kasirRoutes.js", "fullPath": "/api/staff/kasir/payments/:id"}, {"method": "GET", "path": "/pending-payments", "file": "routes/kasirRoutes.js", "fullPath": "/api/staff/kasir/pending-payments"}, {"method": "POST", "path": "/payments/manual", "file": "routes/kasirRoutes.js", "fullPath": "/api/staff/kasir/payments/manual"}, {"method": "POST", "path": "/payments/debug", "file": "routes/kasirRoutes.js", "fullPath": "/api/staff/kasir/payments/debug"}, {"method": "PUT", "path": "/payments/:id/confirm", "file": "routes/kasirRoutes.js", "fullPath": "/api/staff/kasir/payments/:id/confirm"}, {"method": "GET", "path": "/statistics", "file": "routes/kasirRoutes.js", "fullPath": "/api/staff/kasir/statistics"}, {"method": "GET", "path": "/daily-report", "file": "routes/kasirRoutes.js", "fullPath": "/api/staff/kasir/daily-report"}, {"method": "GET", "path": "/bookings", "file": "routes/kasirRoutes.js", "fullPath": "/api/staff/kasir/bookings"}, {"method": "GET", "path": "/bookings/:id", "file": "routes/kasirRoutes.js", "fullPath": "/api/staff/kasir/bookings/:id"}, {"method": "GET", "path": "/dashboard", "file": "routes/kasirRoutes.js", "fullPath": "/api/staff/kasir/dashboard"}, {"method": "GET", "path": "/payment-methods", "file": "routes/kasirRoutes.js", "fullPath": "/api/staff/kasir/payment-methods"}], "routes/operatorRoutes.js": [{"method": "GET", "path": "/dashboard", "file": "routes/operatorRoutes.js", "fullPath": "/api/staff/operator/dashboard"}, {"method": "GET", "path": "/fields", "file": "routes/operatorRoutes.js", "fullPath": "/api/staff/operator/fields"}, {"method": "PUT", "path": "/fields/:id/status", "file": "routes/operatorRoutes.js", "fullPath": "/api/staff/operator/fields/:id/status"}, {"method": "GET", "path": "/fields/:field_id/bookings", "file": "routes/operatorRoutes.js", "fullPath": "/api/staff/operator/fields/:field_id/bookings"}, {"method": "GET", "path": "/schedule/today", "file": "routes/operatorRoutes.js", "fullPath": "/api/staff/operator/schedule/today"}, {"method": "GET", "path": "/today-schedule", "file": "routes/operatorRoutes.js", "fullPath": "/api/staff/operator/today-schedule"}, {"method": "GET", "path": "/schedule/:date", "file": "routes/operatorRoutes.js", "fullPath": "/api/staff/operator/schedule/:date"}, {"method": "PUT", "path": "/bookings/:id/confirm", "file": "routes/operatorRoutes.js", "fullPath": "/api/staff/operator/bookings/:id/confirm"}, {"method": "PUT", "path": "/bookings/:id/complete", "file": "routes/operatorRoutes.js", "fullPath": "/api/staff/operator/bookings/:id/complete"}, {"method": "GET", "path": "/bookings", "file": "routes/operatorRoutes.js", "fullPath": "/api/staff/operator/bookings"}, {"method": "GET", "path": "/bookings/:id", "file": "routes/operatorRoutes.js", "fullPath": "/api/staff/operator/bookings/:id"}, {"method": "GET", "path": "/bookings/pending", "file": "routes/operatorRoutes.js", "fullPath": "/api/staff/operator/bookings/pending"}, {"method": "GET", "path": "/field-statuses", "file": "routes/operatorRoutes.js", "fullPath": "/api/staff/operator/field-statuses"}, {"method": "GET", "path": "/booking-actions", "file": "routes/operatorRoutes.js", "fullPath": "/api/staff/operator/booking-actions"}, {"method": "GET", "path": "/statistics", "file": "routes/operatorRoutes.js", "fullPath": "/api/staff/operator/statistics"}], "routes/manajerRoutes.js": [], "routes/supervisorRoutes.js": [{"method": "GET", "path": "/dashboard", "file": "routes/supervisorRoutes.js", "fullPath": "/api/staff/supervisor/dashboard"}, {"method": "GET", "path": "/system-health", "file": "routes/supervisorRoutes.js", "fullPath": "/api/staff/supervisor/system-health"}, {"method": "POST", "path": "/staff", "file": "routes/supervisorRoutes.js", "fullPath": "/api/staff/supervisor/staff"}, {"method": "GET", "path": "/users", "file": "routes/supervisorRoutes.js", "fullPath": "/api/staff/supervisor/users"}, {"method": "PUT", "path": "/users/:id/role", "file": "routes/supervisorRoutes.js", "fullPath": "/api/staff/supervisor/users/:id/role"}, {"method": "GET", "path": "/analytics", "file": "routes/supervisorRoutes.js", "fullPath": "/api/staff/supervisor/analytics"}, {"method": "GET", "path": "/audit-logs", "file": "routes/supervisorRoutes.js", "fullPath": "/api/staff/supervisor/audit-logs"}, {"method": "GET", "path": "/system-config", "file": "routes/supervisorRoutes.js", "fullPath": "/api/staff/supervisor/system-config"}, {"method": "POST", "path": "/system-maintenance", "file": "routes/supervisorRoutes.js", "fullPath": "/api/staff/supervisor/system-maintenance"}, {"method": "GET", "path": "/database-stats", "file": "routes/supervisorRoutes.js", "fullPath": "/api/staff/supervisor/database-stats"}, {"method": "GET", "path": "/error-logs", "file": "routes/supervisorRoutes.js", "fullPath": "/api/staff/supervisor/error-logs"}], "routes/adminRoutes.js": [{"method": "GET", "path": "/settings", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/settings"}, {"method": "GET", "path": "/settings/public", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/settings/public"}, {"method": "GET", "path": "/settings/:key", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/settings/:key"}, {"method": "PUT", "path": "/settings/:key", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/settings/:key"}, {"method": "POST", "path": "/settings", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/settings"}, {"method": "DELETE", "path": "/settings/:key", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/settings/:key"}, {"method": "GET", "path": "/settings/category/:category", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/settings/category/:category"}, {"method": "PUT", "path": "/settings/bulk-update", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/settings/bulk-update"}, {"method": "POST", "path": "/settings/initialize", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/settings/initialize"}, {"method": "PUT", "path": "/settings/:key/reset", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/settings/:key/reset"}, {"method": "GET", "path": "/audit-logs", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/audit-logs"}, {"method": "GET", "path": "/audit-logs/statistics", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/audit-logs/statistics"}, {"method": "GET", "path": "/audit-logs/record/:tableName/:recordId", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/audit-logs/record/:tableName/:recordId"}, {"method": "GET", "path": "/audit-logs/:id", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/audit-logs/:id"}, {"method": "GET", "path": "/audit-logs/active-users", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/audit-logs/active-users"}, {"method": "GET", "path": "/audit-logs/user/:userId", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/audit-logs/user/:userId"}, {"method": "GET", "path": "/audit-logs/table/:tableName", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/audit-logs/table/:tableName"}, {"method": "DELETE", "path": "/audit-logs/cleanup", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/audit-logs/cleanup"}, {"method": "GET", "path": "/audit-logs/export", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/audit-logs/export"}, {"method": "GET", "path": "/notifications", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/notifications"}, {"method": "POST", "path": "/notifications", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/notifications"}, {"method": "POST", "path": "/notifications/broadcast", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/notifications/broadcast"}, {"method": "GET", "path": "/notifications/statistics", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/notifications/statistics"}, {"method": "GET", "path": "/notifications/:id/status", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/notifications/:id/status"}, {"method": "DELETE", "path": "/notifications/:id", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/notifications/:id"}, {"method": "GET", "path": "/notifications/user/:userId", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/notifications/user/:userId"}, {"method": "GET", "path": "/promotions", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/promotions"}, {"method": "POST", "path": "/promotions", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/promotions"}, {"method": "PUT", "path": "/promotions/:id", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/promotions/:id"}, {"method": "DELETE", "path": "/promotions/:id", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/promotions/:id"}, {"method": "GET", "path": "/promotions/:id/usage", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/promotions/:id/usage"}, {"method": "GET", "path": "/promotions/analytics", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/promotions/analytics"}, {"method": "PUT", "path": "/promotions/:id/toggle", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/promotions/:id/toggle"}, {"method": "GET", "path": "/role-management/dashboard", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/role-management/dashboard"}, {"method": "GET", "path": "/users", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/users"}, {"method": "GET", "path": "/role-management/users", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/role-management/users"}, {"method": "POST", "path": "/role-management/request-change", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/role-management/request-change"}, {"method": "PUT", "path": "/role-management/change-role", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/role-management/change-role"}, {"method": "GET", "path": "/users/:id", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/users/:id"}, {"method": "PUT", "path": "/users/:id", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/users/:id"}, {"method": "DELETE", "path": "/users/:id", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/users/:id"}, {"method": "GET", "path": "/fields", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/fields"}, {"method": "GET", "path": "/fields/:id", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/fields/:id"}, {"method": "POST", "path": "/fields", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/fields"}, {"method": "PUT", "path": "/fields/:id", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/fields/:id"}, {"method": "DELETE", "path": "/fields/:id", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/fields/:id"}, {"method": "GET", "path": "/analytics/business", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/analytics/business"}, {"method": "GET", "path": "/analytics/system", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/analytics/system"}, {"method": "GET", "path": "/analytics/performance", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/analytics/performance"}, {"method": "GET", "path": "/bookings/statistics", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/bookings/statistics"}, {"method": "GET", "path": "/bookings", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/bookings"}, {"method": "GET", "path": "/bookings/:id", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/bookings/:id"}, {"method": "PUT", "path": "/bookings/:id/status", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/bookings/:id/status"}, {"method": "POST", "path": "/auto-completion/trigger", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/auto-completion/trigger"}, {"method": "GET", "path": "/auto-completion/eligible", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/auto-completion/eligible"}, {"method": "GET", "path": "/auto-completion/stats", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/auto-completion/stats"}, {"method": "POST", "path": "/auto-completion/manual/:id", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/auto-completion/manual/:id"}, {"method": "GET", "path": "/auto-completion/config", "file": "routes/adminRoutes.js", "fullPath": "/api/admin/auto-completion/config"}], "routes/enhancedRoutes.js": [{"method": "GET", "path": "/", "file": "routes/enhancedRoutes.js", "fullPath": "/api/enhanced/"}, {"method": "GET", "path": "/features", "file": "routes/enhancedRoutes.js", "fullPath": "/api/enhanced/features"}, {"method": "GET", "path": "/architecture", "file": "routes/enhancedRoutes.js", "fullPath": "/api/enhanced/architecture"}, {"method": "GET", "path": "/status", "file": "routes/enhancedRoutes.js", "fullPath": "/api/enhanced/status"}]}, "by_method": {"GET": 130, "POST": 31, "PUT": 20, "DELETE": 9}, "all_endpoints": ["DELETE /api/admin/audit-logs/cleanup", "DELETE /api/admin/fields/:id", "DELETE /api/admin/notifications/:id", "DELETE /api/admin/promotions/:id", "DELETE /api/admin/settings/:key", "DELETE /api/admin/users/:id", "DELETE /api/customer/favorites/:fieldId", "DELETE /api/customer/notifications/:id", "DELETE /api/customer/reviews/:id", "GET /api/", "GET /api/admin/analytics/business", "GET /api/admin/analytics/performance", "GET /api/admin/analytics/system", "GET /api/admin/audit-logs", "GET /api/admin/audit-logs/:id", "GET /api/admin/audit-logs/active-users", "GET /api/admin/audit-logs/export", "GET /api/admin/audit-logs/record/:tableName/:recordId", "GET /api/admin/audit-logs/statistics", "GET /api/admin/audit-logs/table/:tableName", "GET /api/admin/audit-logs/user/:userId", "GET /api/admin/auto-completion/config", "GET /api/admin/auto-completion/eligible", "GET /api/admin/auto-completion/stats", "GET /api/admin/bookings", "GET /api/admin/bookings/:id", "GET /api/admin/bookings/statistics", "GET /api/admin/fields", "GET /api/admin/fields/:id", "GET /api/admin/notifications", "GET /api/admin/notifications/:id/status", "GET /api/admin/notifications/statistics", "GET /api/admin/notifications/user/:userId", "GET /api/admin/promotions", "GET /api/admin/promotions/:id/usage", "GET /api/admin/promotions/analytics", "GET /api/admin/role-management/dashboard", "GET /api/admin/role-management/users", "GET /api/admin/settings", "GET /api/admin/settings/:key", "GET /api/admin/settings/category/:category", "GET /api/admin/settings/public", "GET /api/admin/users", "GET /api/admin/users/:id", "GET /api/auth/hash-password/:password", "GET /api/auth/health", "GET /api/auth/profile", "GET /api/auth/reset-password/:token", "GET /api/auth/roles", "GET /api/auth/test-features", "GET /api/auth/test-smtp", "GET /api/auth/verification-status/:email", "GET /api/auth/verify", "GET /api/customer/booking-history", "GET /api/customer/bookings", "GET /api/customer/bookings/:bookingId/can-review", "GET /api/customer/bookings/:id", "GET /api/customer/bookings/history", "GET /api/customer/dashboard", "GET /api/customer/favorites", "GET /api/customer/favorites/:fieldId/check", "GET /api/customer/favorites/availability", "GET /api/customer/favorites/count", "GET /api/customer/favorites/statistics", "GET /api/customer/fields", "GET /api/customer/notifications", "GET /api/customer/notifications/count", "GET /api/customer/notifications/statistics", "GET /api/customer/notifications/unread-count", "GET /api/customer/profile", "GET /api/customer/promotions", "GET /api/customer/promotions/:code", "GET /api/customer/recommendations", "GET /api/customer/reviews", "GET /api/customer/reviews/:id", "GET /api/customer/upcoming-bookings", "GET /api/enhanced/", "GET /api/enhanced/architecture", "GET /api/enhanced/features", "GET /api/enhanced/status", "GET /api/health", "GET /api/public/app-config", "GET /api/public/database-status", "GET /api/public/debug/table/:tableName", "GET /api/public/debug/test-promotion", "GET /api/public/debug/test-settings", "GET /api/public/field-locations", "GET /api/public/field-types", "GET /api/public/fields", "GET /api/public/fields/:fieldId/promotions", "GET /api/public/fields/:fieldId/rating", "GET /api/public/fields/:fieldId/reviews", "GET /api/public/fields/:id", "GET /api/public/fields/:id/availability", "GET /api/public/health", "GET /api/public/promotions", "GET /api/public/system-info", "GET /api/public/version", "GET /api/routes", "GET /api/staff/kasir/bookings", "GET /api/staff/kasir/bookings/:id", "GET /api/staff/kasir/daily-report", "GET /api/staff/kasir/dashboard", "GET /api/staff/kasir/payment-methods", "GET /api/staff/kasir/payments", "GET /api/staff/kasir/payments/:id", "GET /api/staff/kasir/payments/pending", "GET /api/staff/kasir/pending-payments", "GET /api/staff/kasir/statistics", "GET /api/staff/operator/booking-actions", "GET /api/staff/operator/bookings", "GET /api/staff/operator/bookings/:id", "GET /api/staff/operator/bookings/pending", "GET /api/staff/operator/dashboard", "GET /api/staff/operator/field-statuses", "GET /api/staff/operator/fields", "GET /api/staff/operator/fields/:field_id/bookings", "GET /api/staff/operator/schedule/:date", "GET /api/staff/operator/schedule/today", "GET /api/staff/operator/statistics", "GET /api/staff/operator/today-schedule", "GET /api/staff/supervisor/analytics", "GET /api/staff/supervisor/audit-logs", "GET /api/staff/supervisor/dashboard", "GET /api/staff/supervisor/database-stats", "GET /api/staff/supervisor/error-logs", "GET /api/staff/supervisor/system-config", "GET /api/staff/supervisor/system-health", "GET /api/staff/supervisor/users", "GET /api/test/admin", "GET /api/test/auth", "GET /api/test/customer", "GET /api/test/database", "GET /api/test/environment", "GET /api/test/health", "GET /api/test/memory", "GET /api/test/public", "GET /api/test/routes", "GET /api/test/staff", "POST /api/admin/auto-completion/manual/:id", "POST /api/admin/auto-completion/trigger", "POST /api/admin/fields", "POST /api/admin/notifications", "POST /api/admin/notifications/broadcast", "POST /api/admin/promotions", "POST /api/admin/role-management/request-change", "POST /api/admin/settings", "POST /api/admin/settings/initialize", "POST /api/auth/change-password", "POST /api/auth/forgot-password", "POST /api/auth/login", "POST /api/auth/logout", "POST /api/auth/refresh", "POST /api/auth/register", "POST /api/auth/reset-password", "POST /api/auth/send-verification", "POST /api/auth/validate-email", "POST /api/auth/validate-email-batch", "POST /api/auth/validate-email-quick", "POST /api/auth/verify-email", "POST /api/customer/bookings", "POST /api/customer/favorites/:fieldId", "POST /api/customer/promotions/apply", "POST /api/customer/promotions/calculate", "POST /api/customer/promotions/validate", "POST /api/customer/reviews", "POST /api/staff/kasir/payments/debug", "POST /api/staff/kasir/payments/manual", "POST /api/staff/supervisor/staff", "POST /api/staff/supervisor/system-maintenance", "PUT /api/admin/bookings/:id/status", "PUT /api/admin/fields/:id", "PUT /api/admin/promotions/:id", "PUT /api/admin/promotions/:id/toggle", "PUT /api/admin/role-management/change-role", "PUT /api/admin/settings/:key", "PUT /api/admin/settings/:key/reset", "PUT /api/admin/settings/bulk-update", "PUT /api/admin/users/:id", "PUT /api/customer/bookings/:id/cancel", "PUT /api/customer/favorites/:fieldId/toggle", "PUT /api/customer/notifications/:id/read", "PUT /api/customer/notifications/read-all", "PUT /api/customer/profile", "PUT /api/customer/reviews/:id", "PUT /api/staff/kasir/payments/:id/confirm", "PUT /api/staff/operator/bookings/:id/complete", "PUT /api/staff/operator/bookings/:id/confirm", "PUT /api/staff/operator/fields/:id/status", "PUT /api/staff/supervisor/users/:id/role"], "generated_at": "2025-06-06T10:06:46.437Z"}