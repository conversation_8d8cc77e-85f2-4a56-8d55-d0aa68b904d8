# =====================================================
# FUTSAL BOOKING SYSTEM - ENVIRONMENT VARIABLES
# =====================================================

# Database Configuration
DATABASE_URL=postgresql://username:password@host:port/database_name

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=24h

# Server Configuration
PORT=5000
NODE_ENV=production

# Frontend Configuration
FRONTEND_URL=https://your-frontend-domain.vercel.app

# App Configuration
APP_NAME=Futsal Booking System

# =====================================================
# EMAIL SERVICE CONFIGURATION (GRATIS)
# =====================================================

# Gmail SMTP (Gratis - gunakan App Password)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Alternative: SendGrid (Gratis tier 100 emails/day)
# SMTP_HOST=smtp.sendgrid.net
# SMTP_PORT=587
# SMTP_USER=apikey
# SMTP_PASS=your-sendgrid-api-key

# =====================================================
# MAPS & WEATHER FEATURES REMOVED
# =====================================================

# Google Maps and Weather API features have been removed
# Focus on core booking functionality only

# =====================================================
# OPTIONAL: FUTURE INTEGRATIONS
# =====================================================

# Payment Gateway (Midtrans)
# MIDTRANS_SERVER_KEY=your-midtrans-server-key
# MIDTRANS_CLIENT_KEY=your-midtrans-client-key
# MIDTRANS_IS_PRODUCTION=false

# SMS Service (Twilio)
# TWILIO_ACCOUNT_SID=your-twilio-account-sid
# TWILIO_AUTH_TOKEN=your-twilio-auth-token
# TWILIO_PHONE_NUMBER=your-twilio-phone-number

# WhatsApp Business API
# WHATSAPP_API_TOKEN=your-whatsapp-api-token
# WHATSAPP_PHONE_NUMBER_ID=your-phone-number-id

# =====================================================
# SETUP INSTRUCTIONS
# =====================================================

# 1. EMAIL SERVICE SETUP (GRATIS):
#    - Buat App Password di Gmail: https://myaccount.google.com/apppasswords
#    - Atau daftar SendGrid: https://sendgrid.com/free/
#    - Set SMTP_USER dan SMTP_PASS

# 2. DEPLOY TO RAILWAY:
#    - Copy semua environment variables ke Railway dashboard
#    - Pastikan DATABASE_URL sudah benar
#    - Deploy dan test!
