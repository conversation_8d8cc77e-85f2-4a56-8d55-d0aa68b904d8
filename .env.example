# Environment Configuration Template
# Copy this file to .env.development or .env.production and fill in your values

# Application Environment
NODE_ENV=development
PORT=5000

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/database_name

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here

# Optional: Additional Configuration
# CORS_ORIGIN=http://localhost:3000
# RATE_LIMIT_WINDOW_MS=900000
# RATE_LIMIT_MAX_REQUESTS=100
